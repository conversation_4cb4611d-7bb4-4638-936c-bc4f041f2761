import { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import cpanelService from '../services/cpanelService';
import { useAuth } from '../context/AuthContext';

const Register = () => {
  const navigate = useNavigate();
  const { register: authRegister } = useAuth();
  const [activeTab, setActiveTab] = useState('username');
  const [formData, setFormData] = useState({
    domainType: 'username',
    username: '',
    projectName: '',
    email: '',
    password: '',
    confirmPassword: '',
    agreeTerms: false
  });
  const [isAvailable, setIsAvailable] = useState(null);
  const [isChecking, setIsChecking] = useState(false);
  const [isRegistering, setIsRegistering] = useState(false);
  const [registrationError, setRegistrationError] = useState(null);
  const [registrationSuccess, setRegistrationSuccess] = useState(false);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));

    // Reset availability when username/projectName changes
    if (name === 'username' || name === 'projectName') {
      setIsAvailable(null);
    }
  };

  const handleTabChange = (tab) => {
    setActiveTab(tab);
    setFormData(prev => ({
      ...prev,
      domainType: tab
    }));
    setIsAvailable(null);
    setRegistrationError(null);
  };

  const validateForm = () => {
    // Validate domain name
    const domainToCheck = formData.domainType === 'username'
      ? formData.username
      : formData.projectName;

    if (!domainToCheck) {
      setRegistrationError('Domain name is required.');
      return false;
    }

    // Validate domain name format
    const domainRegex = /^[a-zA-Z0-9-]+$/;
    if (!domainRegex.test(domainToCheck)) {
      setRegistrationError('Domain name can only contain letters, numbers, and hyphens.');
      return false;
    }

    // Validate email
    if (!formData.email) {
      setRegistrationError('Email is required.');
      return false;
    }

    const emailRegex = /\S+@\S+\.\S+/;
    if (!emailRegex.test(formData.email)) {
      setRegistrationError('Please enter a valid email address.');
      return false;
    }

    // Validate password
    if (!formData.password) {
      setRegistrationError('Password is required.');
      return false;
    }

    if (formData.password.length < 8) {
      setRegistrationError('Password must be at least 8 characters long.');
      return false;
    }

    // Validate password confirmation
    if (formData.password !== formData.confirmPassword) {
      setRegistrationError('Passwords do not match.');
      return false;
    }

    // Validate terms agreement
    if (!formData.agreeTerms) {
      setRegistrationError('You must agree to the terms and conditions.');
      return false;
    }

    return true;
  };

  const checkAvailability = async () => {
    const domainToCheck = formData.domainType === 'username'
      ? formData.username
      : formData.projectName;

    if (!domainToCheck) {
      return;
    }

    // Validate domain name format
    const domainRegex = /^[a-zA-Z0-9-]+$/;
    if (!domainRegex.test(domainToCheck)) {
      setRegistrationError('Domain name can only contain letters, numbers, and hyphens.');
      return;
    }

    setIsChecking(true);
    setRegistrationError(null);

    try {
      // Simulate blockchain verification with a delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Use the cPanel service to check domain availability
      const available = await cpanelService.checkSubdomainAvailability(domainToCheck, formData.domainType);
      setIsAvailable(available);
    } catch (error) {
      console.error('Error checking domain availability:', error);
      setRegistrationError('There was an error checking domain availability. Please try again.');
    } finally {
      setIsChecking(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate the form
    if (!validateForm()) {
      return;
    }

    // Check if domain is available
    if (!isAvailable) {
      setRegistrationError('Please check domain availability first.');
      return;
    }

    setIsRegistering(true);
    setRegistrationError(null);

    try {
      // First register the user with our authentication system
      const authResult = await authRegister(
        formData.username,
        formData.email,
        formData.password
      );

      if (!authResult.success) {
        setRegistrationError(authResult.error || 'User registration failed. Please try again.');
        return;
      }

      // Then register the domain with cPanel
      const result = await cpanelService.registerDomain(formData);

      if (result.success) {
        console.log('Registration successful:', result);
        setRegistrationSuccess(true);

        // Redirect to dashboard after a short delay
        setTimeout(() => {
          navigate('/dashboard');
        }, 2000);
      } else {
        setRegistrationError(result.message || 'Domain registration failed. Please try again.');
        console.error('Registration failed:', result);
      }
    } catch (error) {
      console.error('Error during registration:', error);
      setRegistrationError('An unexpected error occurred. Please try again later.');
    } finally {
      setIsRegistering(false);
    }
  };

  const renderDomainPreview = () => {
    if (formData.domainType === 'username') {
      return (
        <div className="text-center mb-6 bg-bg-light p-4 rounded border border-border">
          <p className="text-text-secondary mb-2">Domain Preview</p>
          <p className="text-xl font-semibold text-accent">
            dyn.zkd.app/<span className="text-text-primary">{formData.username || 'username'}</span>
          </p>
        </div>
      );
    } else {
      return (
        <div className="text-center mb-6 bg-bg-light p-4 rounded border border-border">
          <p className="text-text-secondary mb-2">Domain Preview</p>
          <p className="text-xl font-semibold text-accent">
            <span className="text-text-primary">{formData.projectName || 'project'}</span>.zkd.app
          </p>
        </div>
      );
    }
  };

  const renderAvailabilityMessage = () => {
    if (isChecking) {
      return (
        <div className="mt-2 text-text-secondary flex items-center">
          <span className="mr-2">Checking availability...</span>
          <span className="inline-block w-4 h-4 border-2 border-transparent border-t-accent rounded-full animate-spin"></span>
        </div>
      );
    }

    if (isAvailable === true) {
      return (
        <div className="mt-2 text-green-500 flex items-center">
          <span className="inline-block mr-2">✓</span>
          Domain available
        </div>
      );
    }

    if (isAvailable === false) {
      return (
        <div className="mt-2 text-red-500 flex items-center">
          <span className="inline-block mr-2">✗</span>
          Domain already registered
        </div>
      );
    }

    return null;
  };

  return (
    <div className="bg-primary">
      {/* Simple Header/Navigation */}
      <header className="bg-secondary py-3 shadow-md">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center">
            <Link to="/" className="text-accent text-xl font-bold">ZKD.APP</Link>

            <nav className="flex items-center space-x-6">
              <Link to="/" className="text-white hover:text-accent transition-colors">Home</Link>
              <Link to="/register" className="text-accent font-medium">Register</Link>
              <Link to="/login" className="text-white hover:text-accent transition-colors">Login</Link>
              <Link to="/pricing" className="text-white hover:text-accent transition-colors">Pricing</Link>
              <Link to="/about" className="text-white hover:text-accent transition-colors">About</Link>
              <Link to="/contact" className="text-white hover:text-accent transition-colors">Contact</Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Register Section */}
      <section className="section">
        <div className="container">
          <h1 className="text-xl mb-4">Domain Registration</h1>

          {/* Registration Tabs */}
          <div className="tabs">
            <button
              className={`tab ${activeTab === 'username' ? 'active' : ''}`}
              onClick={() => handleTabChange('username')}
            >
              Personal
            </button>
            <button
              className={`tab ${activeTab === 'project' ? 'active' : ''}`}
              onClick={() => handleTabChange('project')}
            >
              Project
            </button>
          </div>

          {/* Registration Form */}
          <div className="max-w-2xl mx-auto">
            {registrationSuccess ? (
              <div>
                <div className="card text-center">
                  <div className="card-header">
                    <div className="success-text text-5xl mb-2">✓</div>
                    <h2 className="text-xl">Domain Registration Successful!</h2>
                  </div>

                  <div className="card-body">
                    <p className="mb-3">
                      Domain: <span className="text-accent">{formData.domainType === 'username' ? 'dyn.zkd.app/' : ''}{formData.domainType === 'username' ? formData.username : formData.projectName}{formData.domainType === 'project' ? '.zkd.app' : ''}</span>
                    </p>
                    <p className="mb-3">Status: Active</p>
                    <p className="mb-3">Registration Date: {new Date().toISOString().split('T')[0]}</p>
                    <p className="mb-3">Owner Email: {formData.email}</p>

                    <p className="mt-6 mb-4">
                      A confirmation email has been sent to your email address with setup instructions.
                    </p>
                  </div>

                  <div className="card-footer">
                    <div className="flex justify-center">
                      <Link to="/" className="btn mr-4">
                        Return Home
                      </Link>
                      <Link
                        to={formData.domainType === 'username' ? `/username/${formData.username}` : `https://${formData.projectName}.zkd.app`}
                        className="btn btn-primary"
                      >
                        Access Domain
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <form onSubmit={handleSubmit}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div>
                    {/* Domain Preview */}
                    <div className="card mb-4">
                      <div className="card-header">
                        <h3 className="text-lg">Domain Preview</h3>
                      </div>
                      <div className="card-body">
                        <div className="bg-tertiary p-3 rounded text-center">
                          <span className="text-accent">
                            {formData.domainType === 'username' ? 'dyn.zkd.app/' : ''}{formData.domainType === 'username' ? (formData.username || 'username') : (formData.projectName || 'project')}{formData.domainType === 'project' ? '.zkd.app' : ''}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="mb-4">
                      <label
                        htmlFor={formData.domainType === 'username' ? 'username' : 'projectName'}
                        className="block text-secondary mb-2"
                      >
                        {formData.domainType === 'username' ? 'Username' : 'Project Name'}
                      </label>
                      <div className="flex">
                        <input
                          type="text"
                          id={formData.domainType === 'username' ? 'username' : 'projectName'}
                          name={formData.domainType === 'username' ? 'username' : 'projectName'}
                          value={formData.domainType === 'username' ? formData.username : formData.projectName}
                          onChange={handleChange}
                          className="w-full bg-secondary text-primary p-2 border border-border rounded-l"
                          placeholder={formData.domainType === 'username' ? 'your-username' : 'your-project'}
                          required
                        />
                        <button
                          type="button"
                          onClick={checkAvailability}
                          className="btn rounded-l-none"
                          disabled={isChecking}
                        >
                          {isChecking ? '...' : 'Check'}
                        </button>
                      </div>

                      {renderAvailabilityMessage()}

                      <p className="text-tertiary text-sm mt-2">
                        {formData.domainType === 'username'
                          ? 'Username can only contain letters, numbers, and hyphens.'
                          : 'Project name can only contain letters, numbers, and hyphens.'}
                      </p>
                    </div>

                    <div className="mb-4">
                      <label htmlFor="email" className="block text-secondary mb-2">Email Address</label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleChange}
                        className="w-full bg-secondary text-primary p-2 border border-border rounded"
                        placeholder="<EMAIL>"
                        required
                      />
                      <p className="text-tertiary text-sm mt-2">
                        Verification and domain management instructions will be sent to this address.
                      </p>
                    </div>

                    <div className="mb-4">
                      <label htmlFor="password" className="block text-secondary mb-2">Password</label>
                      <input
                        type="password"
                        id="password"
                        name="password"
                        value={formData.password}
                        onChange={handleChange}
                        className="w-full bg-secondary text-primary p-2 border border-border rounded"
                        placeholder="••••••••"
                        required
                      />
                      <p className="text-tertiary text-sm mt-2">
                        Password must be at least 8 characters long.
                      </p>
                    </div>

                    <div className="mb-4">
                      <label htmlFor="confirmPassword" className="block text-secondary mb-2">Confirm Password</label>
                      <input
                        type="password"
                        id="confirmPassword"
                        name="confirmPassword"
                        value={formData.confirmPassword}
                        onChange={handleChange}
                        className="w-full bg-secondary text-primary p-2 border border-border rounded"
                        placeholder="••••••••"
                        required
                      />
                    </div>

                    <div className="mb-6">
                      <label className="flex items-start">
                        <input
                          type="checkbox"
                          name="agreeTerms"
                          checked={formData.agreeTerms}
                          onChange={handleChange}
                          className="mt-1 mr-2"
                          required
                        />
                        <span className="text-tertiary text-sm">
                          I agree to the <Link to="/terms" className="text-accent">Terms of Service</Link> and <Link to="/privacy" className="text-accent">Privacy Policy</Link>
                        </span>
                      </label>
                    </div>

                    {registrationError && (
                      <div className="mb-6 p-3 bg-error bg-opacity-10 text-error border border-error">
                        <p>{registrationError}</p>
                      </div>
                    )}

                    <button
                      type="submit"
                      className="btn btn-primary w-full"
                      disabled={!isAvailable || !formData.email || !formData.agreeTerms || isRegistering}
                    >
                      {isRegistering ? (
                        <span className="flex items-center justify-center">
                          <span className="inline-block w-4 h-4 border-2 border-transparent border-t-white rounded-full animate-spin mr-2"></span>
                          Registering...
                        </span>
                      ) : (
                        `Register ${formData.domainType === 'username' ? 'Personal' : 'Project'} Domain`
                      )}
                    </button>
                  </div>

                  <div>
                    <div className="card mb-4">
                      <div className="card-header">
                        <h3 className="text-lg">{formData.domainType === 'username' ? 'Personal' : 'Project'} Domain Information</h3>
                      </div>

                      <div className="card-body">
                        <p className="mb-3">
                          Format: <span className="text-accent">{formData.domainType === 'username' ? 'dyn.zkd.app/username' : 'projectname.zkd.app'}</span>
                        </p>

                        <h4 className="text-md mb-2">Features:</h4>
                        <ul className="mb-4">
                          {formData.domainType === 'username' ? (
                            <>
                              <li className="mb-1">• Custom username of your choice</li>
                              <li className="mb-1">• Easy sharing with friends and colleagues</li>
                              <li className="mb-1">• Personalized profile page</li>
                              <li className="mb-1">• Link to your social media accounts</li>
                              <li className="mb-1">• Custom redirects to any URL</li>
                            </>
                          ) : (
                            <>
                              <li className="mb-1">• Professional project branding</li>
                              <li className="mb-1">• Dedicated subdomain for your application</li>
                              <li className="mb-1">• Custom DNS settings</li>
                              <li className="mb-1">• SSL certificate included</li>
                              <li className="mb-1">• API access for programmatic management (coming soon)</li>
                            </>
                          )}
                        </ul>
                      </div>
                    </div>

                    <div className="card">
                      <div className="card-header">
                        <h3 className="text-lg">Registration Process</h3>
                      </div>

                      <div className="card-body">
                        <div className="grid grid-cols-1 gap-2">
                          <div className="flex items-start">
                            <div className="text-accent mr-2">1.</div>
                            <div>Choose domain type (personal or project)</div>
                          </div>
                          <div className="flex items-start">
                            <div className="text-accent mr-2">2.</div>
                            <div>Enter domain name and check availability</div>
                          </div>
                          <div className="flex items-start">
                            <div className="text-accent mr-2">3.</div>
                            <div>Provide contact email</div>
                          </div>
                          <div className="flex items-start">
                            <div className="text-accent mr-2">4.</div>
                            <div>Submit registration request</div>
                          </div>
                          <div className="flex items-start">
                            <div className="text-accent mr-2">5.</div>
                            <div>Receive confirmation and setup instructions</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </form>
            )}
          </div>
        </div>
      </section>

      {/* Simple Footer */}
      <footer className="bg-secondary py-3 mt-8">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center">
            <div className="text-secondary">
              &copy; {new Date().getFullYear()} zkd.app
            </div>
            <div className="flex space-x-4">
              <Link to="/privacy" className="text-secondary hover:text-white transition-colors">Privacy</Link>
              <Link to="/terms" className="text-secondary hover:text-white transition-colors">Terms</Link>
              <Link to="/contact" className="text-secondary hover:text-white transition-colors">Contact</Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Register;
