import Link from "next/link";

export default function Home() {
  return (
    <div className="min-h-screen bg-slate-900 text-white">
      {/* Navigation */}
      <nav className="border-b border-slate-800">
        <div className="max-w-6xl mx-auto px-6 py-4">
          <div className="flex justify-between items-center">
            <div className="text-xl font-semibold">ZKD.APP</div>
            <div className="flex items-center space-x-6">
              <Link href="/login" className="text-slate-300 hover:text-white">
                Login
              </Link>
              <Link
                href="/register"
                className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded text-white"
              >
                Register
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-6 py-20">
        {/* Hero Section */}
        <div className="text-center mb-20">
          <h1 className="text-5xl font-bold mb-6">
            Professional Domain Management
          </h1>
          <p className="text-xl text-slate-300 mb-8 max-w-2xl mx-auto">
            Register and manage your domains on zkd.app. Simple, secure, and professional.
          </p>
          <div className="flex justify-center space-x-4">
            <Link
              href="/register"
              className="bg-blue-600 hover:bg-blue-700 px-8 py-3 rounded-lg text-lg font-medium"
            >
              Get Started
            </Link>
            <Link
              href="/login"
              className="bg-slate-700 hover:bg-slate-600 px-8 py-3 rounded-lg text-lg font-medium"
            >
              Sign In
            </Link>
          </div>
        </div>

        {/* Features */}
        <div className="grid md:grid-cols-3 gap-8 mb-20">
          <div className="text-center p-6">
            <h3 className="text-xl font-semibold mb-3">Fast Setup</h3>
            <p className="text-slate-300">
              Get your domain registered and configured in minutes
            </p>
          </div>
          <div className="text-center p-6">
            <h3 className="text-xl font-semibold mb-3">Secure</h3>
            <p className="text-slate-300">
              Enterprise-grade security for all your domains
            </p>
          </div>
          <div className="text-center p-6">
            <h3 className="text-xl font-semibold mb-3">Simple</h3>
            <p className="text-slate-300">
              Clean dashboard to manage everything in one place
            </p>
          </div>
        </div>

        {/* CTA */}
        <div className="text-center">
          <h2 className="text-3xl font-bold mb-4">Ready to get started?</h2>
          <p className="text-slate-300 mb-8">
            Join developers and creators using ZKD.APP for their domains
          </p>
          <Link
            href="/register"
            className="bg-blue-600 hover:bg-blue-700 px-8 py-3 rounded-lg text-lg font-medium"
          >
            Register Your Domain
          </Link>
        </div>
      </main>
    </div>
  );
}
