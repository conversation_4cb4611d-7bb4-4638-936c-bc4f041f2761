import React, { createContext, useState, useEffect, useContext } from 'react';

// Create the authentication context
const AuthContext = createContext();

// Custom hook to use the auth context
export const useAuth = () => {
  return useContext(AuthContext);
};

export const AuthProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(true);
  
  // Check if user is already logged in (from localStorage)
  useEffect(() => {
    const user = localStorage.getItem('user');
    if (user) {
      setCurrentUser(JSON.parse(user));
    }
    setLoading(false);
  }, []);
  
  // Login function
  const login = async (email, password) => {
    try {
      // This would be replaced with an actual API call
      // For now, we'll simulate a successful login
      if (email && password) {
        const userData = {
          id: '1',
          email,
          username: email.split('@')[0],
          name: 'User Example',
          accountType: 'Pro',
          createdAt: '2023-01-15'
        };
        
        // Store user in localStorage
        localStorage.setItem('user', JSON.stringify(userData));
        setCurrentUser(userData);
        return { success: true };
      }
      return { success: false, error: 'Invalid credentials' };
    } catch (error) {
      return { success: false, error: error.message || 'Login failed' };
    }
  };
  
  // Register function
  const register = async (username, email, password) => {
    try {
      // This would be replaced with an actual API call
      // For now, we'll simulate a successful registration
      if (username && email && password) {
        const userData = {
          id: '1',
          email,
          username,
          name: username,
          accountType: 'Free',
          createdAt: new Date().toISOString().split('T')[0]
        };
        
        // Store user in localStorage
        localStorage.setItem('user', JSON.stringify(userData));
        setCurrentUser(userData);
        return { success: true };
      }
      return { success: false, error: 'Registration failed' };
    } catch (error) {
      return { success: false, error: error.message || 'Registration failed' };
    }
  };
  
  // Logout function
  const logout = () => {
    localStorage.removeItem('user');
    setCurrentUser(null);
  };
  
  const value = {
    currentUser,
    login,
    register,
    logout,
    loading
  };
  
  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};
