var lt=Object.defineProperty;var at=(e,r)=>{for(var t in r)lt(e,t,{get:r[t],enumerable:!0})};import*as Z from"node:module";import{pathToFileURL as Ar}from"node:url";var te={};at(te,{DEBUG:()=>ee});var ee=st(process.env.DEBUG);function st(e){if(e===void 0)return!1;if(e==="true"||e==="1")return!0;if(e==="false"||e==="0")return!1;if(e==="*")return!0;let r=e.split(",").map(t=>t.split(":")[0]);return r.includes("-tailwindcss")?!1:!!r.includes("tailwindcss")}import U from"enhanced-resolve";import{createJiti as mr}from"jiti";import ke from"node:fs";import Xe from"node:fs/promises";import ve,{dirname as Ge}from"node:path";import{pathToFileURL as Je}from"node:url";import{__unstable__loadDesignSystem as gr,compile as hr,compileAst as vr,Features as Yl,Polyfills as Ql}from"tailwindcss";import re from"node:fs/promises";import D from"node:path";var ut=[/import[\s\S]*?['"](.{3,}?)['"]/gi,/import[\s\S]*from[\s\S]*?['"](.{3,}?)['"]/gi,/export[\s\S]*from[\s\S]*?['"](.{3,}?)['"]/gi,/require\(['"`](.+)['"`]\)/gi],ft=[".js",".cjs",".mjs"],ct=["",".js",".cjs",".mjs",".ts",".cts",".mts",".jsx",".tsx"],pt=["",".ts",".cts",".mts",".tsx",".js",".cjs",".mjs",".jsx"];async function dt(e,r){for(let t of r){let i=`${e}${t}`;if((await re.stat(i).catch(()=>null))?.isFile())return i}for(let t of r){let i=`${e}/index${t}`;if(await re.access(i).then(()=>!0,()=>!1))return i}return null}async function be(e,r,t,i){let n=ft.includes(i)?ct:pt,o=await dt(D.resolve(t,r),n);if(o===null||e.has(o))return;e.add(o),t=D.dirname(o),i=D.extname(o);let l=await re.readFile(o,"utf-8"),s=[];for(let a of ut)for(let u of l.matchAll(a))u[1].startsWith(".")&&s.push(be(e,u[1],t,i));await Promise.all(s)}async function xe(e){let r=new Set;return await be(r,e,D.dirname(e),D.extname(e)),Array.from(r)}import*as ge from"node:path";function ie(e){return{kind:"word",value:e}}function mt(e,r){return{kind:"function",value:e,nodes:r}}function gt(e){return{kind:"separator",value:e}}function R(e,r,t=null){for(let i=0;i<e.length;i++){let n=e[i],o=!1,l=0,s=r(n,{parent:t,replaceWith(a){o||(o=!0,Array.isArray(a)?a.length===0?(e.splice(i,1),l=0):a.length===1?(e[i]=a[0],l=1):(e.splice(i,1,...a),l=a.length):e[i]=a)}})??0;if(o){s===0?i--:i+=l-1;continue}if(s===2)return 2;if(s!==1&&n.kind==="function"&&R(n.nodes,r,n)===2)return 2}}function N(e){let r="";for(let t of e)switch(t.kind){case"word":case"separator":{r+=t.value;break}case"function":r+=t.value+"("+N(t.nodes)+")"}return r}var Ae=92,ht=41,Ce=58,$e=44,vt=34,Ne=61,Se=62,Te=60,Ve=10,wt=40,kt=39,Ee=47,Re=32,_e=9;function S(e){e=e.replaceAll(`\r
`,`
`);let r=[],t=[],i=null,n="",o;for(let l=0;l<e.length;l++){let s=e.charCodeAt(l);switch(s){case Ae:{n+=e[l]+e[l+1],l++;break}case Ce:case $e:case Ne:case Se:case Te:case Ve:case Ee:case Re:case _e:{if(n.length>0){let c=ie(n);i?i.nodes.push(c):r.push(c),n=""}let a=l,u=l+1;for(;u<e.length&&(o=e.charCodeAt(u),!(o!==Ce&&o!==$e&&o!==Ne&&o!==Se&&o!==Te&&o!==Ve&&o!==Ee&&o!==Re&&o!==_e));u++);l=u-1;let f=gt(e.slice(a,u));i?i.nodes.push(f):r.push(f);break}case kt:case vt:{let a=l;for(let u=l+1;u<e.length;u++)if(o=e.charCodeAt(u),o===Ae)u+=1;else if(o===s){l=u;break}n+=e.slice(a,l+1);break}case wt:{let a=mt(n,[]);n="",i?i.nodes.push(a):r.push(a),t.push(a),i=a;break}case ht:{let a=t.pop();if(n.length>0){let u=ie(n);a.nodes.push(u),n=""}t.length>0?i=t[t.length-1]:i=null;break}default:n+=String.fromCharCode(s)}}return n.length>0&&r.push(ie(n)),r}var yt=["anchor-size"],Tr=new RegExp(`(${yt.join("|")})\\(`,"g");var Or=new Uint8Array(256);var W=new Uint8Array(256);function g(e,r){let t=0,i=[],n=0,o=e.length,l=r.charCodeAt(0);for(let s=0;s<o;s++){let a=e.charCodeAt(s);if(t===0&&a===l){i.push(e.slice(n,s)),n=s+1;continue}switch(a){case 92:s+=1;break;case 39:case 34:for(;++s<o;){let u=e.charCodeAt(s);if(u===92){s+=1;continue}if(u===a)break}break;case 40:W[t]=41,t++;break;case 91:W[t]=93,t++;break;case 123:W[t]=125,t++;break;case 93:case 125:case 41:t>0&&a===W[t-1]&&t--;break}}return i.push(e.slice(n)),i}var v=class extends Map{constructor(t){super();this.factory=t}get(t){let i=super.get(t);return i===void 0&&(i=this.factory(t,this),this.set(t,i)),i}};var x=/[+-]?\d*\.?\d+(?:[eE][+-]?\d+)?/,Yr=new RegExp(`^${x.source}$`);var Qr=new RegExp(`^${x.source}%$`);var Zr=new RegExp(`^${x.source}s*/s*${x.source}$`);var bt=["cm","mm","Q","in","pc","pt","px","em","ex","ch","rem","lh","rlh","vw","vh","vmin","vmax","vb","vi","svw","svh","lvw","lvh","dvw","dvh","cqw","cqh","cqi","cqb","cqmin","cqmax"],Xr=new RegExp(`^${x.source}(${bt.join("|")})$`);var xt=["deg","rad","grad","turn"],ei=new RegExp(`^${x.source}(${xt.join("|")})$`);var ti=new RegExp(`^${x.source} +${x.source} +${x.source}$`);function k(e){let r=Number(e);return Number.isInteger(r)&&r>=0&&String(r)===String(e)}function K(e,r){if(r===null)return e;let t=Number(r);return Number.isNaN(t)||(r=`${t*100}%`),r==="100%"?e:`color-mix(in oklab, ${e} ${r}, transparent)`}var $t={"--alpha":Nt,"--spacing":St,"--theme":Tt,theme:Vt};function Nt(e,r,t,...i){let[n,o]=g(t,"/").map(l=>l.trim());if(!n||!o)throw new Error(`The --alpha(\u2026) function requires a color and an alpha value, e.g.: \`--alpha(${n||"var(--my-color)"} / ${o||"50%"})\``);if(i.length>0)throw new Error(`The --alpha(\u2026) function only accepts one argument, e.g.: \`--alpha(${n||"var(--my-color)"} / ${o||"50%"})\``);return K(n,o)}function St(e,r,t,...i){if(!t)throw new Error("The --spacing(\u2026) function requires an argument, but received none.");if(i.length>0)throw new Error(`The --spacing(\u2026) function only accepts a single argument, but received ${i.length+1}.`);let n=e.theme.resolve(null,["--spacing"]);if(!n)throw new Error("The --spacing(\u2026) function requires that the `--spacing` theme variable exists, but it was not found.");return`calc(${n} * ${t})`}function Tt(e,r,t,...i){if(!t.startsWith("--"))throw new Error("The --theme(\u2026) function can only be used with CSS variables from your theme.");let n=!1;t.endsWith(" inline")&&(n=!0,t=t.slice(0,-7)),r.kind==="at-rule"&&(n=!0);let o=e.resolveThemeValue(t,n);if(!o){if(i.length>0)return i.join(", ");throw new Error(`Could not resolve value for theme function: \`theme(${t})\`. Consider checking if the variable name is correct or provide a fallback value to silence this error.`)}if(i.length===0)return o;let l=i.join(", ");if(l==="initial")return o;if(o==="initial")return l;if(o.startsWith("var(")||o.startsWith("theme(")||o.startsWith("--theme(")){let s=S(o);return Rt(s,l),N(s)}return o}function Vt(e,r,t,...i){t=Et(t);let n=e.resolveThemeValue(t);if(!n&&i.length>0)return i.join(", ");if(!n)throw new Error(`Could not resolve value for theme function: \`theme(${t})\`. Consider checking if the path is correct or provide a fallback value to silence this error.`);return n}var yi=new RegExp(Object.keys($t).map(e=>`${e}\\(`).join("|"));function Et(e){if(e[0]!=="'"&&e[0]!=='"')return e;let r="",t=e[0];for(let i=1;i<e.length-1;i++){let n=e[i],o=e[i+1];n==="\\"&&(o===t||o==="\\")?(r+=o,i++):r+=n}return r}function Rt(e,r){R(e,t=>{if(t.kind==="function"&&!(t.value!=="var"&&t.value!=="theme"&&t.value!=="--theme"))if(t.nodes.length===1)t.nodes.push({kind:"word",value:`, ${r}`});else{let i=t.nodes[t.nodes.length-1];i.kind==="word"&&i.value==="initial"&&(i.value=r)}})}var I=92,H=47,q=42,Kt=34,Ft=39,It=58,G=59,T=10,z=32,J=9,Ke=123,ae=125,fe=40,Fe=41,zt=91,jt=93,Ie=45,se=64,Lt=33;function L(e){e[0]==="\uFEFF"&&(e=e.slice(1)),e=e.replaceAll(`\r
`,`
`);let r=[],t=[],i=[],n=null,o=null,l="",s="",a;for(let u=0;u<e.length;u++){let f=e.charCodeAt(u);if(f===I)l+=e.slice(u,u+2),u+=1;else if(f===H&&e.charCodeAt(u+1)===q){let c=u;for(let h=u+2;h<e.length;h++)if(a=e.charCodeAt(h),a===I)h+=1;else if(a===q&&e.charCodeAt(h+1)===H){u=h+1;break}let p=e.slice(c,u+1);p.charCodeAt(2)===Lt&&t.push(pe(p.slice(2,-2)))}else if(f===Ft||f===Kt){let c=u;for(let p=u+1;p<e.length;p++)if(a=e.charCodeAt(p),a===I)p+=1;else if(a===f){u=p;break}else{if(a===G&&e.charCodeAt(p+1)===T)throw new Error(`Unterminated string: ${e.slice(c,p+1)+String.fromCharCode(f)}`);if(a===T)throw new Error(`Unterminated string: ${e.slice(c,p)+String.fromCharCode(f)}`)}l+=e.slice(c,u+1)}else{if((f===z||f===T||f===J)&&(a=e.charCodeAt(u+1))&&(a===z||a===T||a===J))continue;if(f===T){if(l.length===0)continue;a=l.charCodeAt(l.length-1),a!==z&&a!==T&&a!==J&&(l+=" ")}else if(f===Ie&&e.charCodeAt(u+1)===Ie&&l.length===0){let c="",p=u,h=-1;for(let m=u+2;m<e.length;m++)if(a=e.charCodeAt(m),a===I)m+=1;else if(a===H&&e.charCodeAt(m+1)===q){for(let E=m+2;E<e.length;E++)if(a=e.charCodeAt(E),a===I)E+=1;else if(a===q&&e.charCodeAt(E+1)===H){m=E+1;break}}else if(h===-1&&a===It)h=l.length+m-p;else if(a===G&&c.length===0){l+=e.slice(p,m),u=m;break}else if(a===fe)c+=")";else if(a===zt)c+="]";else if(a===Ke)c+="}";else if((a===ae||e.length-1===m)&&c.length===0){u=m-1,l+=e.slice(p,m);break}else(a===Fe||a===jt||a===ae)&&c.length>0&&e[m]===c[c.length-1]&&(c=c.slice(0,-1));let X=ue(l,h);if(!X)throw new Error("Invalid custom property, expected a value");n?n.nodes.push(X):r.push(X),l=""}else if(f===G&&l.charCodeAt(0)===se)o=j(l),n?n.nodes.push(o):r.push(o),l="",o=null;else if(f===G&&s[s.length-1]!==")"){let c=ue(l);if(!c)throw l.length===0?new Error("Unexpected semicolon"):new Error(`Invalid declaration: \`${l.trim()}\``);n?n.nodes.push(c):r.push(c),l=""}else if(f===Ke&&s[s.length-1]!==")")s+="}",o=C(l.trim()),n&&n.nodes.push(o),i.push(n),n=o,l="",o=null;else if(f===ae&&s[s.length-1]!==")"){if(s==="")throw new Error("Missing opening {");if(s=s.slice(0,-1),l.length>0)if(l.charCodeAt(0)===se)o=j(l),n?n.nodes.push(o):r.push(o),l="",o=null;else{let p=l.indexOf(":");if(n){let h=ue(l,p);if(!h)throw new Error(`Invalid declaration: \`${l.trim()}\``);n.nodes.push(h)}}let c=i.pop()??null;c===null&&n&&r.push(n),n=c,l="",o=null}else if(f===fe)s+=")",l+="(";else if(f===Fe){if(s[s.length-1]!==")")throw new Error("Missing opening (");s=s.slice(0,-1),l+=")"}else{if(l.length===0&&(f===z||f===T||f===J))continue;l+=String.fromCharCode(f)}}}if(l.charCodeAt(0)===se&&r.push(j(l)),s.length>0&&n){if(n.kind==="rule")throw new Error(`Missing closing } at ${n.selector}`);if(n.kind==="at-rule")throw new Error(`Missing closing } at ${n.name} ${n.params}`)}return t.length>0?t.concat(r):r}function j(e,r=[]){for(let t=5;t<e.length;t++){let i=e.charCodeAt(t);if(i===z||i===fe){let n=e.slice(0,t).trim(),o=e.slice(t).trim();return y(n,o,r)}}return y(e.trim(),"",r)}function ue(e,r=e.indexOf(":")){if(r===-1)return null;let t=e.indexOf("!important",r+1);return A(e.slice(0,r).trim(),e.slice(r+1,t===-1?e.length:t).trim(),t!==-1)}var de={inherit:"inherit",current:"currentcolor",transparent:"transparent",black:"#000",white:"#fff",slate:{50:"oklch(98.4% 0.003 247.858)",100:"oklch(96.8% 0.007 247.896)",200:"oklch(92.9% 0.013 255.508)",300:"oklch(86.9% 0.022 252.894)",400:"oklch(70.4% 0.04 256.788)",500:"oklch(55.4% 0.046 257.417)",600:"oklch(44.6% 0.043 257.281)",700:"oklch(37.2% 0.044 257.287)",800:"oklch(27.9% 0.041 260.031)",900:"oklch(20.8% 0.042 265.755)",950:"oklch(12.9% 0.042 264.695)"},gray:{50:"oklch(98.5% 0.002 247.839)",100:"oklch(96.7% 0.003 264.542)",200:"oklch(92.8% 0.006 264.531)",300:"oklch(87.2% 0.01 258.338)",400:"oklch(70.7% 0.022 261.325)",500:"oklch(55.1% 0.027 264.364)",600:"oklch(44.6% 0.03 256.802)",700:"oklch(37.3% 0.034 259.733)",800:"oklch(27.8% 0.033 256.848)",900:"oklch(21% 0.034 264.665)",950:"oklch(13% 0.028 261.692)"},zinc:{50:"oklch(98.5% 0 0)",100:"oklch(96.7% 0.001 286.375)",200:"oklch(92% 0.004 286.32)",300:"oklch(87.1% 0.006 286.286)",400:"oklch(70.5% 0.015 286.067)",500:"oklch(55.2% 0.016 285.938)",600:"oklch(44.2% 0.017 285.786)",700:"oklch(37% 0.013 285.805)",800:"oklch(27.4% 0.006 286.033)",900:"oklch(21% 0.006 285.885)",950:"oklch(14.1% 0.005 285.823)"},neutral:{50:"oklch(98.5% 0 0)",100:"oklch(97% 0 0)",200:"oklch(92.2% 0 0)",300:"oklch(87% 0 0)",400:"oklch(70.8% 0 0)",500:"oklch(55.6% 0 0)",600:"oklch(43.9% 0 0)",700:"oklch(37.1% 0 0)",800:"oklch(26.9% 0 0)",900:"oklch(20.5% 0 0)",950:"oklch(14.5% 0 0)"},stone:{50:"oklch(98.5% 0.001 106.423)",100:"oklch(97% 0.001 106.424)",200:"oklch(92.3% 0.003 48.717)",300:"oklch(86.9% 0.005 56.366)",400:"oklch(70.9% 0.01 56.259)",500:"oklch(55.3% 0.013 58.071)",600:"oklch(44.4% 0.011 73.639)",700:"oklch(37.4% 0.01 67.558)",800:"oklch(26.8% 0.007 34.298)",900:"oklch(21.6% 0.006 56.043)",950:"oklch(14.7% 0.004 49.25)"},red:{50:"oklch(97.1% 0.013 17.38)",100:"oklch(93.6% 0.032 17.717)",200:"oklch(88.5% 0.062 18.334)",300:"oklch(80.8% 0.114 19.571)",400:"oklch(70.4% 0.191 22.216)",500:"oklch(63.7% 0.237 25.331)",600:"oklch(57.7% 0.245 27.325)",700:"oklch(50.5% 0.213 27.518)",800:"oklch(44.4% 0.177 26.899)",900:"oklch(39.6% 0.141 25.723)",950:"oklch(25.8% 0.092 26.042)"},orange:{50:"oklch(98% 0.016 73.684)",100:"oklch(95.4% 0.038 75.164)",200:"oklch(90.1% 0.076 70.697)",300:"oklch(83.7% 0.128 66.29)",400:"oklch(75% 0.183 55.934)",500:"oklch(70.5% 0.213 47.604)",600:"oklch(64.6% 0.222 41.116)",700:"oklch(55.3% 0.195 38.402)",800:"oklch(47% 0.157 37.304)",900:"oklch(40.8% 0.123 38.172)",950:"oklch(26.6% 0.079 36.259)"},amber:{50:"oklch(98.7% 0.022 95.277)",100:"oklch(96.2% 0.059 95.617)",200:"oklch(92.4% 0.12 95.746)",300:"oklch(87.9% 0.169 91.605)",400:"oklch(82.8% 0.189 84.429)",500:"oklch(76.9% 0.188 70.08)",600:"oklch(66.6% 0.179 58.318)",700:"oklch(55.5% 0.163 48.998)",800:"oklch(47.3% 0.137 46.201)",900:"oklch(41.4% 0.112 45.904)",950:"oklch(27.9% 0.077 45.635)"},yellow:{50:"oklch(98.7% 0.026 102.212)",100:"oklch(97.3% 0.071 103.193)",200:"oklch(94.5% 0.129 101.54)",300:"oklch(90.5% 0.182 98.111)",400:"oklch(85.2% 0.199 91.936)",500:"oklch(79.5% 0.184 86.047)",600:"oklch(68.1% 0.162 75.834)",700:"oklch(55.4% 0.135 66.442)",800:"oklch(47.6% 0.114 61.907)",900:"oklch(42.1% 0.095 57.708)",950:"oklch(28.6% 0.066 53.813)"},lime:{50:"oklch(98.6% 0.031 120.757)",100:"oklch(96.7% 0.067 122.328)",200:"oklch(93.8% 0.127 124.321)",300:"oklch(89.7% 0.196 126.665)",400:"oklch(84.1% 0.238 128.85)",500:"oklch(76.8% 0.233 130.85)",600:"oklch(64.8% 0.2 131.684)",700:"oklch(53.2% 0.157 131.589)",800:"oklch(45.3% 0.124 130.933)",900:"oklch(40.5% 0.101 131.063)",950:"oklch(27.4% 0.072 132.109)"},green:{50:"oklch(98.2% 0.018 155.826)",100:"oklch(96.2% 0.044 156.743)",200:"oklch(92.5% 0.084 155.995)",300:"oklch(87.1% 0.15 154.449)",400:"oklch(79.2% 0.209 151.711)",500:"oklch(72.3% 0.219 149.579)",600:"oklch(62.7% 0.194 149.214)",700:"oklch(52.7% 0.154 150.069)",800:"oklch(44.8% 0.119 151.328)",900:"oklch(39.3% 0.095 152.535)",950:"oklch(26.6% 0.065 152.934)"},emerald:{50:"oklch(97.9% 0.021 166.113)",100:"oklch(95% 0.052 163.051)",200:"oklch(90.5% 0.093 164.15)",300:"oklch(84.5% 0.143 164.978)",400:"oklch(76.5% 0.177 163.223)",500:"oklch(69.6% 0.17 162.48)",600:"oklch(59.6% 0.145 163.225)",700:"oklch(50.8% 0.118 165.612)",800:"oklch(43.2% 0.095 166.913)",900:"oklch(37.8% 0.077 168.94)",950:"oklch(26.2% 0.051 172.552)"},teal:{50:"oklch(98.4% 0.014 180.72)",100:"oklch(95.3% 0.051 180.801)",200:"oklch(91% 0.096 180.426)",300:"oklch(85.5% 0.138 181.071)",400:"oklch(77.7% 0.152 181.912)",500:"oklch(70.4% 0.14 182.503)",600:"oklch(60% 0.118 184.704)",700:"oklch(51.1% 0.096 186.391)",800:"oklch(43.7% 0.078 188.216)",900:"oklch(38.6% 0.063 188.416)",950:"oklch(27.7% 0.046 192.524)"},cyan:{50:"oklch(98.4% 0.019 200.873)",100:"oklch(95.6% 0.045 203.388)",200:"oklch(91.7% 0.08 205.041)",300:"oklch(86.5% 0.127 207.078)",400:"oklch(78.9% 0.154 211.53)",500:"oklch(71.5% 0.143 215.221)",600:"oklch(60.9% 0.126 221.723)",700:"oklch(52% 0.105 223.128)",800:"oklch(45% 0.085 224.283)",900:"oklch(39.8% 0.07 227.392)",950:"oklch(30.2% 0.056 229.695)"},sky:{50:"oklch(97.7% 0.013 236.62)",100:"oklch(95.1% 0.026 236.824)",200:"oklch(90.1% 0.058 230.902)",300:"oklch(82.8% 0.111 230.318)",400:"oklch(74.6% 0.16 232.661)",500:"oklch(68.5% 0.169 237.323)",600:"oklch(58.8% 0.158 241.966)",700:"oklch(50% 0.134 242.749)",800:"oklch(44.3% 0.11 240.79)",900:"oklch(39.1% 0.09 240.876)",950:"oklch(29.3% 0.066 243.157)"},blue:{50:"oklch(97% 0.014 254.604)",100:"oklch(93.2% 0.032 255.585)",200:"oklch(88.2% 0.059 254.128)",300:"oklch(80.9% 0.105 251.813)",400:"oklch(70.7% 0.165 254.624)",500:"oklch(62.3% 0.214 259.815)",600:"oklch(54.6% 0.245 262.881)",700:"oklch(48.8% 0.243 264.376)",800:"oklch(42.4% 0.199 265.638)",900:"oklch(37.9% 0.146 265.522)",950:"oklch(28.2% 0.091 267.935)"},indigo:{50:"oklch(96.2% 0.018 272.314)",100:"oklch(93% 0.034 272.788)",200:"oklch(87% 0.065 274.039)",300:"oklch(78.5% 0.115 274.713)",400:"oklch(67.3% 0.182 276.935)",500:"oklch(58.5% 0.233 277.117)",600:"oklch(51.1% 0.262 276.966)",700:"oklch(45.7% 0.24 277.023)",800:"oklch(39.8% 0.195 277.366)",900:"oklch(35.9% 0.144 278.697)",950:"oklch(25.7% 0.09 281.288)"},violet:{50:"oklch(96.9% 0.016 293.756)",100:"oklch(94.3% 0.029 294.588)",200:"oklch(89.4% 0.057 293.283)",300:"oklch(81.1% 0.111 293.571)",400:"oklch(70.2% 0.183 293.541)",500:"oklch(60.6% 0.25 292.717)",600:"oklch(54.1% 0.281 293.009)",700:"oklch(49.1% 0.27 292.581)",800:"oklch(43.2% 0.232 292.759)",900:"oklch(38% 0.189 293.745)",950:"oklch(28.3% 0.141 291.089)"},purple:{50:"oklch(97.7% 0.014 308.299)",100:"oklch(94.6% 0.033 307.174)",200:"oklch(90.2% 0.063 306.703)",300:"oklch(82.7% 0.119 306.383)",400:"oklch(71.4% 0.203 305.504)",500:"oklch(62.7% 0.265 303.9)",600:"oklch(55.8% 0.288 302.321)",700:"oklch(49.6% 0.265 301.924)",800:"oklch(43.8% 0.218 303.724)",900:"oklch(38.1% 0.176 304.987)",950:"oklch(29.1% 0.149 302.717)"},fuchsia:{50:"oklch(97.7% 0.017 320.058)",100:"oklch(95.2% 0.037 318.852)",200:"oklch(90.3% 0.076 319.62)",300:"oklch(83.3% 0.145 321.434)",400:"oklch(74% 0.238 322.16)",500:"oklch(66.7% 0.295 322.15)",600:"oklch(59.1% 0.293 322.896)",700:"oklch(51.8% 0.253 323.949)",800:"oklch(45.2% 0.211 324.591)",900:"oklch(40.1% 0.17 325.612)",950:"oklch(29.3% 0.136 325.661)"},pink:{50:"oklch(97.1% 0.014 343.198)",100:"oklch(94.8% 0.028 342.258)",200:"oklch(89.9% 0.061 343.231)",300:"oklch(82.3% 0.12 346.018)",400:"oklch(71.8% 0.202 349.761)",500:"oklch(65.6% 0.241 354.308)",600:"oklch(59.2% 0.249 0.584)",700:"oklch(52.5% 0.223 3.958)",800:"oklch(45.9% 0.187 3.815)",900:"oklch(40.8% 0.153 2.432)",950:"oklch(28.4% 0.109 3.907)"},rose:{50:"oklch(96.9% 0.015 12.422)",100:"oklch(94.1% 0.03 12.58)",200:"oklch(89.2% 0.058 10.001)",300:"oklch(81% 0.117 11.638)",400:"oklch(71.2% 0.194 13.428)",500:"oklch(64.5% 0.246 16.439)",600:"oklch(58.6% 0.253 17.585)",700:"oklch(51.4% 0.222 16.935)",800:"oklch(45.5% 0.188 13.697)",900:"oklch(41% 0.159 10.272)",950:"oklch(27.1% 0.105 12.094)"}};function V(e){return{__BARE_VALUE__:e}}var b=V(e=>{if(k(e.value))return e.value}),d=V(e=>{if(k(e.value))return`${e.value}%`}),$=V(e=>{if(k(e.value))return`${e.value}px`}),je=V(e=>{if(k(e.value))return`${e.value}ms`}),Y=V(e=>{if(k(e.value))return`${e.value}deg`}),Yt=V(e=>{if(e.fraction===null)return;let[r,t]=g(e.fraction,"/");if(!(!k(r)||!k(t)))return e.fraction}),Le=V(e=>{if(k(Number(e.value)))return`repeat(${e.value}, minmax(0, 1fr))`}),Qt={accentColor:({theme:e})=>e("colors"),animation:{none:"none",spin:"spin 1s linear infinite",ping:"ping 1s cubic-bezier(0, 0, 0.2, 1) infinite",pulse:"pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite",bounce:"bounce 1s infinite"},aria:{busy:'busy="true"',checked:'checked="true"',disabled:'disabled="true"',expanded:'expanded="true"',hidden:'hidden="true"',pressed:'pressed="true"',readonly:'readonly="true"',required:'required="true"',selected:'selected="true"'},aspectRatio:{auto:"auto",square:"1 / 1",video:"16 / 9",...Yt},backdropBlur:({theme:e})=>e("blur"),backdropBrightness:({theme:e})=>({...e("brightness"),...d}),backdropContrast:({theme:e})=>({...e("contrast"),...d}),backdropGrayscale:({theme:e})=>({...e("grayscale"),...d}),backdropHueRotate:({theme:e})=>({...e("hueRotate"),...Y}),backdropInvert:({theme:e})=>({...e("invert"),...d}),backdropOpacity:({theme:e})=>({...e("opacity"),...d}),backdropSaturate:({theme:e})=>({...e("saturate"),...d}),backdropSepia:({theme:e})=>({...e("sepia"),...d}),backgroundColor:({theme:e})=>e("colors"),backgroundImage:{none:"none","gradient-to-t":"linear-gradient(to top, var(--tw-gradient-stops))","gradient-to-tr":"linear-gradient(to top right, var(--tw-gradient-stops))","gradient-to-r":"linear-gradient(to right, var(--tw-gradient-stops))","gradient-to-br":"linear-gradient(to bottom right, var(--tw-gradient-stops))","gradient-to-b":"linear-gradient(to bottom, var(--tw-gradient-stops))","gradient-to-bl":"linear-gradient(to bottom left, var(--tw-gradient-stops))","gradient-to-l":"linear-gradient(to left, var(--tw-gradient-stops))","gradient-to-tl":"linear-gradient(to top left, var(--tw-gradient-stops))"},backgroundOpacity:({theme:e})=>e("opacity"),backgroundPosition:{bottom:"bottom",center:"center",left:"left","left-bottom":"left bottom","left-top":"left top",right:"right","right-bottom":"right bottom","right-top":"right top",top:"top"},backgroundSize:{auto:"auto",cover:"cover",contain:"contain"},blur:{0:"0",none:"",sm:"4px",DEFAULT:"8px",md:"12px",lg:"16px",xl:"24px","2xl":"40px","3xl":"64px"},borderColor:({theme:e})=>({DEFAULT:"currentcolor",...e("colors")}),borderOpacity:({theme:e})=>e("opacity"),borderRadius:{none:"0px",sm:"0.125rem",DEFAULT:"0.25rem",md:"0.375rem",lg:"0.5rem",xl:"0.75rem","2xl":"1rem","3xl":"1.5rem",full:"9999px"},borderSpacing:({theme:e})=>e("spacing"),borderWidth:{DEFAULT:"1px",0:"0px",2:"2px",4:"4px",8:"8px",...$},boxShadow:{sm:"0 1px 2px 0 rgb(0 0 0 / 0.05)",DEFAULT:"0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)",md:"0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)",lg:"0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)",xl:"0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)","2xl":"0 25px 50px -12px rgb(0 0 0 / 0.25)",inner:"inset 0 2px 4px 0 rgb(0 0 0 / 0.05)",none:"none"},boxShadowColor:({theme:e})=>e("colors"),brightness:{0:"0",50:".5",75:".75",90:".9",95:".95",100:"1",105:"1.05",110:"1.1",125:"1.25",150:"1.5",200:"2",...d},caretColor:({theme:e})=>e("colors"),colors:()=>({...de}),columns:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12","3xs":"16rem","2xs":"18rem",xs:"20rem",sm:"24rem",md:"28rem",lg:"32rem",xl:"36rem","2xl":"42rem","3xl":"48rem","4xl":"56rem","5xl":"64rem","6xl":"72rem","7xl":"80rem",...b},container:{},content:{none:"none"},contrast:{0:"0",50:".5",75:".75",100:"1",125:"1.25",150:"1.5",200:"2",...d},cursor:{auto:"auto",default:"default",pointer:"pointer",wait:"wait",text:"text",move:"move",help:"help","not-allowed":"not-allowed",none:"none","context-menu":"context-menu",progress:"progress",cell:"cell",crosshair:"crosshair","vertical-text":"vertical-text",alias:"alias",copy:"copy","no-drop":"no-drop",grab:"grab",grabbing:"grabbing","all-scroll":"all-scroll","col-resize":"col-resize","row-resize":"row-resize","n-resize":"n-resize","e-resize":"e-resize","s-resize":"s-resize","w-resize":"w-resize","ne-resize":"ne-resize","nw-resize":"nw-resize","se-resize":"se-resize","sw-resize":"sw-resize","ew-resize":"ew-resize","ns-resize":"ns-resize","nesw-resize":"nesw-resize","nwse-resize":"nwse-resize","zoom-in":"zoom-in","zoom-out":"zoom-out"},divideColor:({theme:e})=>e("borderColor"),divideOpacity:({theme:e})=>e("borderOpacity"),divideWidth:({theme:e})=>({...e("borderWidth"),...$}),dropShadow:{sm:"0 1px 1px rgb(0 0 0 / 0.05)",DEFAULT:["0 1px 2px rgb(0 0 0 / 0.1)","0 1px 1px rgb(0 0 0 / 0.06)"],md:["0 4px 3px rgb(0 0 0 / 0.07)","0 2px 2px rgb(0 0 0 / 0.06)"],lg:["0 10px 8px rgb(0 0 0 / 0.04)","0 4px 3px rgb(0 0 0 / 0.1)"],xl:["0 20px 13px rgb(0 0 0 / 0.03)","0 8px 5px rgb(0 0 0 / 0.08)"],"2xl":"0 25px 25px rgb(0 0 0 / 0.15)",none:"0 0 #0000"},fill:({theme:e})=>e("colors"),flex:{1:"1 1 0%",auto:"1 1 auto",initial:"0 1 auto",none:"none"},flexBasis:({theme:e})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",...e("spacing")}),flexGrow:{0:"0",DEFAULT:"1",...b},flexShrink:{0:"0",DEFAULT:"1",...b},fontFamily:{sans:["ui-sans-serif","system-ui","sans-serif",'"Apple Color Emoji"','"Segoe UI Emoji"','"Segoe UI Symbol"','"Noto Color Emoji"'],serif:["ui-serif","Georgia","Cambria",'"Times New Roman"',"Times","serif"],mono:["ui-monospace","SFMono-Regular","Menlo","Monaco","Consolas",'"Liberation Mono"','"Courier New"',"monospace"]},fontSize:{xs:["0.75rem",{lineHeight:"1rem"}],sm:["0.875rem",{lineHeight:"1.25rem"}],base:["1rem",{lineHeight:"1.5rem"}],lg:["1.125rem",{lineHeight:"1.75rem"}],xl:["1.25rem",{lineHeight:"1.75rem"}],"2xl":["1.5rem",{lineHeight:"2rem"}],"3xl":["1.875rem",{lineHeight:"2.25rem"}],"4xl":["2.25rem",{lineHeight:"2.5rem"}],"5xl":["3rem",{lineHeight:"1"}],"6xl":["3.75rem",{lineHeight:"1"}],"7xl":["4.5rem",{lineHeight:"1"}],"8xl":["6rem",{lineHeight:"1"}],"9xl":["8rem",{lineHeight:"1"}]},fontWeight:{thin:"100",extralight:"200",light:"300",normal:"400",medium:"500",semibold:"600",bold:"700",extrabold:"800",black:"900"},gap:({theme:e})=>e("spacing"),gradientColorStops:({theme:e})=>e("colors"),gradientColorStopPositions:{"0%":"0%","5%":"5%","10%":"10%","15%":"15%","20%":"20%","25%":"25%","30%":"30%","35%":"35%","40%":"40%","45%":"45%","50%":"50%","55%":"55%","60%":"60%","65%":"65%","70%":"70%","75%":"75%","80%":"80%","85%":"85%","90%":"90%","95%":"95%","100%":"100%",...d},grayscale:{0:"0",DEFAULT:"100%",...d},gridAutoColumns:{auto:"auto",min:"min-content",max:"max-content",fr:"minmax(0, 1fr)"},gridAutoRows:{auto:"auto",min:"min-content",max:"max-content",fr:"minmax(0, 1fr)"},gridColumn:{auto:"auto","span-1":"span 1 / span 1","span-2":"span 2 / span 2","span-3":"span 3 / span 3","span-4":"span 4 / span 4","span-5":"span 5 / span 5","span-6":"span 6 / span 6","span-7":"span 7 / span 7","span-8":"span 8 / span 8","span-9":"span 9 / span 9","span-10":"span 10 / span 10","span-11":"span 11 / span 11","span-12":"span 12 / span 12","span-full":"1 / -1"},gridColumnEnd:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...b},gridColumnStart:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...b},gridRow:{auto:"auto","span-1":"span 1 / span 1","span-2":"span 2 / span 2","span-3":"span 3 / span 3","span-4":"span 4 / span 4","span-5":"span 5 / span 5","span-6":"span 6 / span 6","span-7":"span 7 / span 7","span-8":"span 8 / span 8","span-9":"span 9 / span 9","span-10":"span 10 / span 10","span-11":"span 11 / span 11","span-12":"span 12 / span 12","span-full":"1 / -1"},gridRowEnd:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...b},gridRowStart:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...b},gridTemplateColumns:{none:"none",subgrid:"subgrid",1:"repeat(1, minmax(0, 1fr))",2:"repeat(2, minmax(0, 1fr))",3:"repeat(3, minmax(0, 1fr))",4:"repeat(4, minmax(0, 1fr))",5:"repeat(5, minmax(0, 1fr))",6:"repeat(6, minmax(0, 1fr))",7:"repeat(7, minmax(0, 1fr))",8:"repeat(8, minmax(0, 1fr))",9:"repeat(9, minmax(0, 1fr))",10:"repeat(10, minmax(0, 1fr))",11:"repeat(11, minmax(0, 1fr))",12:"repeat(12, minmax(0, 1fr))",...Le},gridTemplateRows:{none:"none",subgrid:"subgrid",1:"repeat(1, minmax(0, 1fr))",2:"repeat(2, minmax(0, 1fr))",3:"repeat(3, minmax(0, 1fr))",4:"repeat(4, minmax(0, 1fr))",5:"repeat(5, minmax(0, 1fr))",6:"repeat(6, minmax(0, 1fr))",7:"repeat(7, minmax(0, 1fr))",8:"repeat(8, minmax(0, 1fr))",9:"repeat(9, minmax(0, 1fr))",10:"repeat(10, minmax(0, 1fr))",11:"repeat(11, minmax(0, 1fr))",12:"repeat(12, minmax(0, 1fr))",...Le},height:({theme:e})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%",full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),hueRotate:{0:"0deg",15:"15deg",30:"30deg",60:"60deg",90:"90deg",180:"180deg",...Y},inset:({theme:e})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%",full:"100%",...e("spacing")}),invert:{0:"0",DEFAULT:"100%",...d},keyframes:{spin:{to:{transform:"rotate(360deg)"}},ping:{"75%, 100%":{transform:"scale(2)",opacity:"0"}},pulse:{"50%":{opacity:".5"}},bounce:{"0%, 100%":{transform:"translateY(-25%)",animationTimingFunction:"cubic-bezier(0.8,0,1,1)"},"50%":{transform:"none",animationTimingFunction:"cubic-bezier(0,0,0.2,1)"}}},letterSpacing:{tighter:"-0.05em",tight:"-0.025em",normal:"0em",wide:"0.025em",wider:"0.05em",widest:"0.1em"},lineHeight:{none:"1",tight:"1.25",snug:"1.375",normal:"1.5",relaxed:"1.625",loose:"2",3:".75rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem"},listStyleType:{none:"none",disc:"disc",decimal:"decimal"},listStyleImage:{none:"none"},margin:({theme:e})=>({auto:"auto",...e("spacing")}),lineClamp:{1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",...b},maxHeight:({theme:e})=>({none:"none",full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),maxWidth:({theme:e})=>({none:"none",xs:"20rem",sm:"24rem",md:"28rem",lg:"32rem",xl:"36rem","2xl":"42rem","3xl":"48rem","4xl":"56rem","5xl":"64rem","6xl":"72rem","7xl":"80rem",full:"100%",min:"min-content",max:"max-content",fit:"fit-content",prose:"65ch",...e("spacing")}),minHeight:({theme:e})=>({full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),minWidth:({theme:e})=>({full:"100%",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),objectPosition:{bottom:"bottom",center:"center",left:"left","left-bottom":"left bottom","left-top":"left top",right:"right","right-bottom":"right bottom","right-top":"right top",top:"top"},opacity:{0:"0",5:"0.05",10:"0.1",15:"0.15",20:"0.2",25:"0.25",30:"0.3",35:"0.35",40:"0.4",45:"0.45",50:"0.5",55:"0.55",60:"0.6",65:"0.65",70:"0.7",75:"0.75",80:"0.8",85:"0.85",90:"0.9",95:"0.95",100:"1",...d},order:{first:"-9999",last:"9999",none:"0",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",...b},outlineColor:({theme:e})=>e("colors"),outlineOffset:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...$},outlineWidth:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...$},padding:({theme:e})=>e("spacing"),placeholderColor:({theme:e})=>e("colors"),placeholderOpacity:({theme:e})=>e("opacity"),ringColor:({theme:e})=>({DEFAULT:"currentcolor",...e("colors")}),ringOffsetColor:({theme:e})=>e("colors"),ringOffsetWidth:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...$},ringOpacity:({theme:e})=>({DEFAULT:"0.5",...e("opacity")}),ringWidth:{DEFAULT:"3px",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...$},rotate:{0:"0deg",1:"1deg",2:"2deg",3:"3deg",6:"6deg",12:"12deg",45:"45deg",90:"90deg",180:"180deg",...Y},saturate:{0:"0",50:".5",100:"1",150:"1.5",200:"2",...d},scale:{0:"0",50:".5",75:".75",90:".9",95:".95",100:"1",105:"1.05",110:"1.1",125:"1.25",150:"1.5",...d},screens:{sm:"40rem",md:"48rem",lg:"64rem",xl:"80rem","2xl":"96rem"},scrollMargin:({theme:e})=>e("spacing"),scrollPadding:({theme:e})=>e("spacing"),sepia:{0:"0",DEFAULT:"100%",...d},skew:{0:"0deg",1:"1deg",2:"2deg",3:"3deg",6:"6deg",12:"12deg",...Y},space:({theme:e})=>e("spacing"),spacing:{px:"1px",0:"0px",.5:"0.125rem",1:"0.25rem",1.5:"0.375rem",2:"0.5rem",2.5:"0.625rem",3:"0.75rem",3.5:"0.875rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem",11:"2.75rem",12:"3rem",14:"3.5rem",16:"4rem",20:"5rem",24:"6rem",28:"7rem",32:"8rem",36:"9rem",40:"10rem",44:"11rem",48:"12rem",52:"13rem",56:"14rem",60:"15rem",64:"16rem",72:"18rem",80:"20rem",96:"24rem"},stroke:({theme:e})=>({none:"none",...e("colors")}),strokeWidth:{0:"0",1:"1",2:"2",...b},supports:{},data:{},textColor:({theme:e})=>e("colors"),textDecorationColor:({theme:e})=>e("colors"),textDecorationThickness:{auto:"auto","from-font":"from-font",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...$},textIndent:({theme:e})=>e("spacing"),textOpacity:({theme:e})=>e("opacity"),textUnderlineOffset:{auto:"auto",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...$},transformOrigin:{center:"center",top:"top","top-right":"top right",right:"right","bottom-right":"bottom right",bottom:"bottom","bottom-left":"bottom left",left:"left","top-left":"top left"},transitionDelay:{0:"0s",75:"75ms",100:"100ms",150:"150ms",200:"200ms",300:"300ms",500:"500ms",700:"700ms",1e3:"1000ms",...je},transitionDuration:{DEFAULT:"150ms",0:"0s",75:"75ms",100:"100ms",150:"150ms",200:"200ms",300:"300ms",500:"500ms",700:"700ms",1e3:"1000ms",...je},transitionProperty:{none:"none",all:"all",DEFAULT:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter",colors:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke",opacity:"opacity",shadow:"box-shadow",transform:"transform"},transitionTimingFunction:{DEFAULT:"cubic-bezier(0.4, 0, 0.2, 1)",linear:"linear",in:"cubic-bezier(0.4, 0, 1, 1)",out:"cubic-bezier(0, 0, 0.2, 1)","in-out":"cubic-bezier(0.4, 0, 0.2, 1)"},translate:({theme:e})=>({"1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%",full:"100%",...e("spacing")}),size:({theme:e})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),width:({theme:e})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",screen:"100vw",svw:"100svw",lvw:"100lvw",dvw:"100dvw",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),willChange:{auto:"auto",scroll:"scroll-position",contents:"contents",transform:"transform"},zIndex:{auto:"auto",0:"0",10:"10",20:"20",30:"30",40:"40",50:"50",...b}};var Zt=64;function O(e,r=[]){return{kind:"rule",selector:e,nodes:r}}function y(e,r="",t=[]){return{kind:"at-rule",name:e,params:r,nodes:t}}function C(e,r=[]){return e.charCodeAt(0)===Zt?j(e,r):O(e,r)}function A(e,r,t=!1){return{kind:"declaration",property:e,value:r,important:t}}function pe(e){return{kind:"comment",value:e}}function w(e,r,t=[],i={}){for(let n=0;n<e.length;n++){let o=e[n],l=t[t.length-1]??null;if(o.kind==="context"){if(w(o.nodes,r,t,{...i,...o.context})===2)return 2;continue}t.push(o);let s=!1,a=0,u=r(o,{parent:l,context:i,path:t,replaceWith(f){s||(s=!0,Array.isArray(f)?f.length===0?(e.splice(n,1),a=0):f.length===1?(e[n]=f[0],a=1):(e.splice(n,1,...f),a=f.length):(e[n]=f,a=1))}})??0;if(t.pop(),s){u===0?n--:n+=a-1;continue}if(u===2)return 2;if(u!==1&&"nodes"in o){t.push(o);let f=w(o.nodes,r,t,i);if(t.pop(),f===2)return 2}}}function P(e){function r(i,n=0){let o="",l="  ".repeat(n);if(i.kind==="declaration")o+=`${l}${i.property}: ${i.value}${i.important?" !important":""};
`;else if(i.kind==="rule"){o+=`${l}${i.selector} {
`;for(let s of i.nodes)o+=r(s,n+1);o+=`${l}}
`}else if(i.kind==="at-rule"){if(i.nodes.length===0)return`${l}${i.name} ${i.params};
`;o+=`${l}${i.name}${i.params?` ${i.params} `:" "}{
`;for(let s of i.nodes)o+=r(s,n+1);o+=`${l}}
`}else if(i.kind==="comment")o+=`${l}/*${i.value}*/
`;else if(i.kind==="context"||i.kind==="at-root")return"";return o}let t="";for(let i of e){let n=r(i);n!==""&&(t+=n)}return t}function Xt(e,r){if(typeof e!="string")throw new TypeError("expected path to be a string");if(e==="\\"||e==="/")return"/";var t=e.length;if(t<=1)return e;var i="";if(t>4&&e[3]==="\\"){var n=e[2];(n==="?"||n===".")&&e.slice(0,2)==="\\\\"&&(e=e.slice(2),i="//")}var o=e.split(/[/\\]+/);return r!==!1&&o[o.length-1]===""&&o.pop(),i+o.join("/")}function me(e){let r=Xt(e);return e.startsWith("\\\\")&&r.startsWith("/")&&!r.startsWith("//")?`/${r}`:r}var he=/(?<!@import\s+)(?<=^|[^\w\-\u0080-\uffff])url\((\s*('[^']+'|"[^"]+")\s*|[^'")]+)\)/,Me=/(?<=image-set\()((?:[\w-]{1,256}\([^)]*\)|[^)])*)(?=\))/,er=/(?:gradient|element|cross-fade|image)\(/,tr=/^\s*data:/i,rr=/^([a-z]+:)?\/\//,ir=/^[A-Z_][.\w-]*\(/i,nr=/(?:^|\s)(?<url>[\w-]+\([^)]*\)|"[^"]*"|'[^']*'|[^,]\S*[^,])\s*(?:\s(?<descriptor>\w[^,]+))?(?:,|$)/g,or=/(?<!\\)"/g,lr=/(?: |\\t|\\n|\\f|\\r)+/g,ar=e=>tr.test(e),sr=e=>rr.test(e);async function We({css:e,base:r,root:t}){if(!e.includes("url(")&&!e.includes("image-set("))return e;let i=L(e),n=[];function o(l){if(l[0]==="/")return l;let s=ge.posix.join(me(r),l),a=ge.posix.relative(me(t),s);return a.startsWith(".")||(a="./"+a),a}return w(i,l=>{if(l.kind!=="declaration"||!l.value)return;let s=he.test(l.value),a=Me.test(l.value);if(s||a){let u=a?ur:Be;n.push(u(l.value,o).then(f=>{l.value=f}))}}),n.length&&await Promise.all(n),P(i)}function Be(e,r){return qe(e,he,async t=>{let[i,n]=t;return await He(n.trim(),i,r)})}async function ur(e,r){return await qe(e,Me,async t=>{let[,i]=t;return await cr(i,async({url:o})=>he.test(o)?await Be(o,r):er.test(o)?o:await He(o,o,r))})}async function He(e,r,t,i="url"){let n="",o=e[0];if((o==='"'||o==="'")&&(n=o,e=e.slice(1,-1)),fr(e))return r;let l=await t(e);return n===""&&l!==encodeURI(l)&&(n='"'),n==="'"&&l.includes("'")&&(n='"'),n==='"'&&l.includes('"')&&(l=l.replace(or,'\\"')),`${i}(${n}${l}${n})`}function fr(e,r){return sr(e)||ar(e)||!e[0].match(/[\.a-zA-Z0-9_]/)||ir.test(e)}function cr(e,r){return Promise.all(pr(e).map(async({url:t,descriptor:i})=>({url:await r({url:t,descriptor:i}),descriptor:i}))).then(dr)}function pr(e){let r=e.trim().replace(lr," ").replace(/\r?\n/,"").replace(/,\s+/,", ").replaceAll(/\s+/g," ").matchAll(nr);return Array.from(r,({groups:t})=>({url:t?.url?.trim()??"",descriptor:t?.descriptor?.trim()??""})).filter(({url:t})=>!!t)}function dr(e){return e.map(({url:r,descriptor:t})=>r+(t?` ${t}`:"")).join(", ")}async function qe(e,r,t){let i,n=e,o="";for(;i=r.exec(n);)o+=n.slice(0,i.index),o+=await t(i),n=n.slice(i.index+i[0].length);return o+=n,o}function et({base:e,polyfills:r,onDependency:t,shouldRewriteUrls:i,customCssResolver:n,customJsResolver:o}){return{base:e,polyfills:r,async loadModule(l,s){return rt(l,s,t,o)},async loadStylesheet(l,s){let a=await it(l,s,t,n);return i&&(a.content=await We({css:a.content,root:e,base:a.base})),a}}}async function tt(e,r){if(e.root&&e.root!=="none"){let t=/[*{]/,i=[];for(let o of e.root.pattern.split("/")){if(t.test(o))break;i.push(o)}if(!await Xe.stat(ve.resolve(r,i.join("/"))).then(o=>o.isDirectory()).catch(()=>!1))throw new Error(`The \`source(${e.root.pattern})\` does not exist`)}}async function ea(e,r){let t=await vr(e,et(r));return await tt(t,r.base),t}async function ta(e,r){let t=await hr(e,et(r));return await tt(t,r.base),t}async function ra(e,{base:r}){return gr(e,{base:r,async loadModule(t,i){return rt(t,i,()=>{})},async loadStylesheet(t,i){return it(t,i,()=>{})}})}async function rt(e,r,t,i){if(e[0]!=="."){let s=await Ze(e,r,i);if(!s)throw new Error(`Could not resolve '${e}' from '${r}'`);let a=await Qe(Je(s).href);return{base:Ge(s),module:a.default??a}}let n=await Ze(e,r,i);if(!n)throw new Error(`Could not resolve '${e}' from '${r}'`);let[o,l]=await Promise.all([Qe(Je(n).href+"?id="+Date.now()),xe(n)]);for(let s of l)t(s);return{base:Ge(n),module:o.default??o}}async function it(e,r,t,i){let n=await kr(e,r,i);if(!n)throw new Error(`Could not resolve '${e}' from '${r}'`);if(t(n),typeof globalThis.__tw_readFile=="function"){let l=await globalThis.__tw_readFile(n,"utf-8");if(l)return{base:ve.dirname(n),content:l}}let o=await Xe.readFile(n,"utf-8");return{base:ve.dirname(n),content:o}}var Ye=null;async function Qe(e){if(typeof globalThis.__tw_load=="function"){let r=await globalThis.__tw_load(e);if(r)return r}try{return await import(e)}catch{return Ye??=mr(import.meta.url,{moduleCache:!1,fsCache:!1}),await Ye.import(e)}}var ye=["node_modules",...process.env.NODE_PATH?[process.env.NODE_PATH]:[]],wr=U.ResolverFactory.createResolver({fileSystem:new U.CachedInputFileSystem(ke,4e3),useSyncFileSystemCalls:!0,extensions:[".css"],mainFields:["style"],conditionNames:["style"],modules:ye});async function kr(e,r,t){if(typeof globalThis.__tw_resolve=="function"){let i=globalThis.__tw_resolve(e,r);if(i)return Promise.resolve(i)}if(t){let i=await t(e,r);if(i)return i}return we(wr,e,r)}var yr=U.ResolverFactory.createResolver({fileSystem:new U.CachedInputFileSystem(ke,4e3),useSyncFileSystemCalls:!0,extensions:[".js",".json",".node",".ts"],conditionNames:["node","import"],modules:ye}),br=U.ResolverFactory.createResolver({fileSystem:new U.CachedInputFileSystem(ke,4e3),useSyncFileSystemCalls:!0,extensions:[".js",".json",".node",".ts"],conditionNames:["node","require"],modules:ye});async function Ze(e,r,t){if(typeof globalThis.__tw_resolve=="function"){let i=globalThis.__tw_resolve(e,r);if(i)return Promise.resolve(i)}if(t){let i=await t(e,r);if(i)return i}return we(yr,e,r).catch(()=>we(br,e,r))}function we(e,r,t){return new Promise((i,n)=>e.resolve({},t,r,{},(o,l)=>{if(o)return n(o);i(l)}))}Symbol.dispose??=Symbol("Symbol.dispose");Symbol.asyncDispose??=Symbol("Symbol.asyncDispose");var nt=class{constructor(r=t=>void process.stderr.write(`${t}
`)){this.defaultFlush=r}#r=new v(()=>({value:0}));#t=new v(()=>({value:0n}));#e=[];hit(r){this.#r.get(r).value++}start(r){let t=this.#e.map(n=>n.label).join("//"),i=`${t}${t.length===0?"":"//"}${r}`;this.#r.get(i).value++,this.#t.get(i),this.#e.push({id:i,label:r,namespace:t,value:process.hrtime.bigint()})}end(r){let t=process.hrtime.bigint();if(this.#e[this.#e.length-1].label!==r)throw new Error(`Mismatched timer label: \`${r}\`, expected \`${this.#e[this.#e.length-1].label}\``);let i=this.#e.pop(),n=t-i.value;this.#t.get(i.id).value+=n}reset(){this.#r.clear(),this.#t.clear(),this.#e.splice(0)}report(r=this.defaultFlush){let t=[],i=!1;for(let l=this.#e.length-1;l>=0;l--)this.end(this.#e[l].label);for(let[l,{value:s}]of this.#r.entries()){if(this.#t.has(l))continue;t.length===0&&(i=!0,t.push("Hits:"));let a=l.split("//").length;t.push(`${"  ".repeat(a)}${l} ${Q(ot(`\xD7 ${s}`))}`)}this.#t.size>0&&i&&t.push(`
Timers:`);let n=-1/0,o=new Map;for(let[l,{value:s}]of this.#t){let a=`${(Number(s)/1e6).toFixed(2)}ms`;o.set(l,a),n=Math.max(n,a.length)}for(let l of this.#t.keys()){let s=l.split("//").length;t.push(`${Q(`[${o.get(l).padStart(n," ")}]`)}${"  ".repeat(s-1)}${s===1?" ":Q(" \u21B3 ")}${l.split("//").pop()} ${this.#r.get(l).value===1?"":Q(ot(`\xD7 ${this.#r.get(l).value}`))}`.trimEnd())}r(`
${t.join(`
`)}
`),this.reset()}[Symbol.dispose](){ee&&this.report()}};function Q(e){return`\x1B[2m${e}\x1B[22m`}function ot(e){return`\x1B[34m${e}\x1B[39m`}import{Features as M,transform as xr}from"lightningcss";function aa(e,{file:r="input.css",minify:t=!1}={}){function i(o){return xr({filename:r,code:o,minify:t,sourceMap:!1,drafts:{customMedia:!0},nonStandard:{deepSelectorCombinator:!0},include:M.Nesting|M.MediaQueries,exclude:M.LogicalProperties|M.DirSelector|M.LightDark,targets:{safari:16<<16|1024,ios_saf:16<<16|1024,firefox:8388608,chrome:7274496},errorRecovery:!0}).code}let n=i(i(Buffer.from(e))).toString();return n=n.replaceAll("@media not (","@media not all and ("),n}if(!process.versions.bun){let e=Z.createRequire(import.meta.url);Z.register?.(Ar(e.resolve("@tailwindcss/node/esm-cache-loader")))}export{Yl as Features,nt as Instrumentation,Ql as Polyfills,ra as __unstable__loadDesignSystem,ta as compile,ea as compileAst,te as env,rt as loadModule,me as normalizePath,aa as optimize};
