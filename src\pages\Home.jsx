import { Link } from 'react-router-dom'

const Home = () => {
  return (
    <div className="min-h-screen bg-slate-900">
      {/* Hero Section */}
      <section className="py-16">
        <div className="max-w-4xl mx-auto px-6 text-center">
          <h1 className="text-3xl font-bold text-white mb-4">
            Your Domain, Your Identity
          </h1>
          <p className="text-lg text-slate-300 mb-8 max-w-2xl mx-auto">
            Register your personal domain on zkd.app and create a professional online presence.
            Simple, fast, and secure domain management.
          </p>
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Link to="/register" className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded text-sm font-medium">
              Get Started
            </Link>
            <Link to="/login" className="bg-slate-700 hover:bg-slate-600 text-white px-6 py-2 rounded text-sm font-medium">
              Sign In
            </Link>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-12 bg-slate-800">
        <div className="max-w-4xl mx-auto px-6">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold text-white mb-2">
              Why Choose ZKD.APP?
            </h2>
            <p className="text-slate-300">
              Everything you need to manage your domains professionally
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-6">
            <div className="bg-slate-700 rounded-lg p-6 text-center">
              <h3 className="text-lg font-semibold text-white mb-3">Fast Setup</h3>
              <p className="text-slate-300 text-sm">
                Get your domain up and running in minutes, not hours.
              </p>
            </div>

            <div className="bg-slate-700 rounded-lg p-6 text-center">
              <h3 className="text-lg font-semibold text-white mb-3">Secure</h3>
              <p className="text-slate-300 text-sm">
                Built with security in mind. Your data is safe with us.
              </p>
            </div>

            <div className="bg-slate-700 rounded-lg p-6 text-center">
              <h3 className="text-lg font-semibold text-white mb-3">Easy Management</h3>
              <p className="text-slate-300 text-sm">
                Intuitive dashboard to manage all your domains in one place.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-12">
        <div className="max-w-4xl mx-auto px-6 text-center">
          <h2 className="text-2xl font-bold text-white mb-4">
            Ready to Get Started?
          </h2>
          <p className="text-slate-300 mb-6">
            Join thousands of users who trust ZKD.APP for their domain needs.
          </p>
          <Link to="/register" className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded text-sm font-medium">
            Register Your Domain
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-slate-800 py-8">
        <div className="max-w-4xl mx-auto px-6">
          <div className="flex flex-col md:flex-row justify-between items-center text-sm">
            <div className="text-slate-400 mb-4 md:mb-0">
              &copy; {new Date().getFullYear()} ZKD.APP
            </div>
            <div className="flex space-x-4">
              <Link to="/privacy" className="text-slate-400 hover:text-white">Privacy</Link>
              <Link to="/terms" className="text-slate-400 hover:text-white">Terms</Link>
              <Link to="/contact" className="text-slate-400 hover:text-white">Contact</Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default Home
