import { Link } from 'react-router-dom'

const Home = () => {
  return (
    <div className="min-h-screen">
      <div className="max-w-4xl mx-auto px-4 py-16">
        {/* Hero */}
        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold text-white mb-6">
            Domain Management Made Simple
          </h1>
          <p className="text-xl text-slate-300 mb-8">
            Register and manage your domains with ZKD.APP
          </p>
          <div className="space-x-4">
            <Link
              to="/register"
              className="inline-block bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium"
            >
              Get Started
            </Link>
            <Link
              to="/login"
              className="inline-block bg-slate-700 hover:bg-slate-600 text-white px-6 py-3 rounded-lg font-medium"
            >
              Sign In
            </Link>
          </div>
        </div>

        {/* Features */}
        <div className="grid md:grid-cols-3 gap-8 mb-16">
          <div className="text-center">
            <h3 className="text-xl font-semibold text-white mb-3">Fast</h3>
            <p className="text-slate-300">Quick domain registration and setup</p>
          </div>
          <div className="text-center">
            <h3 className="text-xl font-semibold text-white mb-3">Secure</h3>
            <p className="text-slate-300">Your domains are protected and secure</p>
          </div>
          <div className="text-center">
            <h3 className="text-xl font-semibold text-white mb-3">Simple</h3>
            <p className="text-slate-300">Easy-to-use management dashboard</p>
          </div>
        </div>

        {/* CTA */}
        <div className="text-center">
          <h2 className="text-2xl font-bold text-white mb-4">
            Ready to start?
          </h2>
          <Link
            to="/register"
            className="inline-block bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-medium"
          >
            Register Your Domain
          </Link>
        </div>
      </div>
    </div>
  )
}

export default Home
