import { Link } from 'react-router-dom'

const Home = () => {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="py-20">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-5xl font-bold mb-6">
            Your Domain, Your Identity
          </h1>
          <p className="text-xl text-slate-400 mb-8 max-w-2xl mx-auto">
            Register your personal domain on zkd.app and create a professional online presence.
            Simple, fast, and secure domain management.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/register" className="btn btn-primary text-lg px-8 py-3">
              Get Started
            </Link>
            <Link to="/login" className="btn btn-secondary text-lg px-8 py-3">
              Sign In
            </Link>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-slate-800">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">
            Why Choose ZKD.APP?
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="card text-center">
              <div className="text-blue-400 text-4xl mb-4">🚀</div>
              <h3 className="text-xl font-semibold mb-3">Fast Setup</h3>
              <p className="text-slate-400">
                Get your domain up and running in minutes, not hours.
              </p>
            </div>
            <div className="card text-center">
              <div className="text-blue-400 text-4xl mb-4">🔒</div>
              <h3 className="text-xl font-semibold mb-3">Secure</h3>
              <p className="text-slate-400">
                Built with security in mind. Your data is safe with us.
              </p>
            </div>
            <div className="card text-center">
              <div className="text-blue-400 text-4xl mb-4">⚡</div>
              <h3 className="text-xl font-semibold mb-3">Easy Management</h3>
              <p className="text-slate-400">
                Intuitive dashboard to manage all your domains in one place.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">
            Ready to Get Started?
          </h2>
          <p className="text-xl text-slate-400 mb-8">
            Join thousands of users who trust ZKD.APP for their domain needs.
          </p>
          <Link to="/register" className="btn btn-primary text-lg px-8 py-3">
            Register Your Domain
          </Link>
        </div>
      </section>
    </div>
  )
}

export default Home
