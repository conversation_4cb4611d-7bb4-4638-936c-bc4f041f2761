import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { createBrowserRouter, RouterProvider } from 'react-router-dom'
import { AuthProvider } from './context/AuthContext'
import './index.css'

// Layouts
import MainLayout from './layouts/MainLayout'

// Pages
import Home from './pages/Home'
import About from './pages/About'
import Contact from './pages/Contact'
import Register from './pages/Register'
import Login from './pages/Login'
import Dashboard from './pages/Dashboard'
import Pricing from './pages/Pricing'
import UserPage from './pages/UserPage'
import DomainDetails from './pages/DomainDetails'
import DomainAnalytics from './pages/DomainAnalytics'
import NotFound from './pages/NotFound'

// Components
import ProtectedRoute from './components/ProtectedRoute'

const router = createBrowserRouter([
  {
    path: '/',
    element: <MainLayout />,
    children: [
      { index: true, element: <Home /> },
      { path: 'register', element: <Register /> },
      { path: 'login', element: <Login /> },
      { path: 'about', element: <About /> },
      { path: 'contact', element: <Contact /> },
      { path: 'dashboard', element: <ProtectedRoute><Dashboard /></ProtectedRoute> },
      { path: 'pricing', element: <Pricing /> },
      { path: 'domain/:domainId/edit', element: <ProtectedRoute><DomainDetails /></ProtectedRoute> },
      { path: 'domain/:domainId/analytics', element: <ProtectedRoute><DomainAnalytics /></ProtectedRoute> },
      // Dynamic route for username domains
      { path: 'username/:username', element: <UserPage /> },
      { path: '*', element: <NotFound /> }
    ]
  }
])

createRoot(document.getElementById('root')).render(
  <StrictMode>
    <AuthProvider>
      <RouterProvider router={router} />
    </AuthProvider>
  </StrictMode>,
)
