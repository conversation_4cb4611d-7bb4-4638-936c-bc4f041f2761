import { useState, useEffect } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import domainService from '../services/domainService';

const DomainAnalytics = () => {
  const { domainId } = useParams();
  const { currentUser } = useAuth();
  const navigate = useNavigate();

  const [domain, setDomain] = useState(null);
  const [analytics, setAnalytics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch domain and analytics data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch domain details
        const domainData = await domainService.getDomainById(domainId);
        setDomain(domainData);

        // Fetch analytics data
        const analyticsData = await domainService.getDomainAnalytics(domainId);
        setAnalytics(analyticsData);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load analytics data. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    if (domainId) {
      fetchData();
    }
  }, [domainId]);

  // Helper function to get the day names for the last 7 days
  const getDayNames = () => {
    const days = [];
    const today = new Date();

    for (let i = 6; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(today.getDate() - i);
      days.push(date.toLocaleDateString('en-US', { weekday: 'short' }));
    }

    return days;
  };

  return (
    <div className="bg-primary min-h-screen">
      {/* Simple Header/Navigation */}
      <header className="bg-secondary py-3 shadow-md">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center">
            <Link to="/" className="text-accent text-xl font-bold">ZKD.APP</Link>

            <nav className="flex items-center space-x-6">
              <Link to="/" className="text-white hover:text-accent transition-colors">Home</Link>
              <Link to="/dashboard" className="text-white hover:text-accent transition-colors">Dashboard</Link>
              <Link to="/pricing" className="text-white hover:text-accent transition-colors">Pricing</Link>
              <button
                onClick={() => navigate(-1)}
                className="text-white hover:text-accent transition-colors"
              >
                Back
              </button>
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-xl font-bold text-white">Domain Analytics</h1>
            {domain && (
              <p className="text-secondary">{domain.url}</p>
            )}
          </div>

          <div className="flex space-x-3">
            <Link
              to={`/domain/${domainId}/edit`}
              className="bg-primary hover:bg-primary-light text-white px-3 py-1 rounded text-sm transition-colors flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
              Edit Domain
            </Link>

            <Link
              to={`/domain/${domainId}/dns`}
              className="bg-primary hover:bg-primary-light text-white px-3 py-1 rounded text-sm transition-colors flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 10-5.656-5.656l-4 4a4 4 0 105.656 5.656l1.102-1.101" />
              </svg>
              DNS Management
            </Link>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center py-8">
            <div className="inline-block w-6 h-6 border-2 border-transparent border-t-accent rounded-full animate-spin"></div>
            <span className="ml-2 text-secondary">Loading analytics data...</span>
          </div>
        ) : error ? (
          <div className="bg-red-900 bg-opacity-20 text-red-400 p-4 rounded text-center">
            {error}
            <button
              className="ml-2 underline"
              onClick={() => window.location.reload()}
            >
              Retry
            </button>
          </div>
        ) : analytics ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Visits Overview */}
            <div className="bg-secondary rounded shadow p-4">
              <h2 className="text-lg font-bold text-white mb-4">Visits Overview</h2>

              <div className="flex items-center justify-between mb-4">
                <div>
                  <p className="text-secondary text-xs">Total Visits</p>
                  <p className="text-white text-2xl font-bold">{domain.visits.toLocaleString()}</p>
                </div>

                <div>
                  <p className="text-secondary text-xs">Last 7 Days</p>
                  <p className="text-white text-2xl font-bold">
                    {analytics.dailyVisits.reduce((sum, visits) => sum + visits, 0).toLocaleString()}
                  </p>
                </div>
              </div>

              {/* Simple Bar Chart */}
              <div className="mt-6">
                <p className="text-secondary text-xs mb-2">Daily Visits (Last 7 Days)</p>
                <div className="flex items-end h-40 space-x-2">
                  {analytics.dailyVisits.map((visits, index) => {
                    const maxVisits = Math.max(...analytics.dailyVisits);
                    const percentage = maxVisits > 0 ? (visits / maxVisits) * 100 : 0;

                    return (
                      <div key={index} className="flex-1 flex flex-col items-center">
                        <div
                          className="w-full bg-accent rounded-t"
                          style={{ height: `${percentage}%` }}
                        ></div>
                        <p className="text-secondary text-xs mt-1">{getDayNames()[index]}</p>
                        <p className="text-white text-xs">{visits}</p>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>

            {/* Referrers */}
            <div className="bg-secondary rounded shadow p-4">
              <h2 className="text-lg font-bold text-white mb-4">Top Referrers</h2>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="text-left text-secondary text-xs border-b border-primary">
                      <th className="px-3 py-2">Source</th>
                      <th className="px-3 py-2">Visits</th>
                      <th className="px-3 py-2">Percentage</th>
                    </tr>
                  </thead>
                  <tbody>
                    {analytics.referrers.map((referrer, index) => {
                      const totalVisits = analytics.referrers.reduce((sum, r) => sum + r.count, 0);
                      const percentage = totalVisits > 0 ? (referrer.count / totalVisits) * 100 : 0;

                      return (
                        <tr key={index} className="border-b border-primary">
                          <td className="px-3 py-2 text-white text-sm">{referrer.source}</td>
                          <td className="px-3 py-2 text-secondary text-sm">{referrer.count.toLocaleString()}</td>
                          <td className="px-3 py-2">
                            <div className="flex items-center">
                              <div className="w-16 bg-primary rounded-full h-2 mr-2">
                                <div
                                  className="bg-accent rounded-full h-2"
                                  style={{ width: `${percentage}%` }}
                                ></div>
                              </div>
                              <span className="text-secondary text-xs">{percentage.toFixed(1)}%</span>
                            </div>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Countries */}
            <div className="bg-secondary rounded shadow p-4">
              <h2 className="text-lg font-bold text-white mb-4">Top Countries</h2>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="text-left text-secondary text-xs border-b border-primary">
                      <th className="px-3 py-2">Country</th>
                      <th className="px-3 py-2">Visits</th>
                      <th className="px-3 py-2">Percentage</th>
                    </tr>
                  </thead>
                  <tbody>
                    {analytics.countries.map((country, index) => {
                      const totalVisits = analytics.countries.reduce((sum, c) => sum + c.count, 0);
                      const percentage = totalVisits > 0 ? (country.count / totalVisits) * 100 : 0;

                      return (
                        <tr key={index} className="border-b border-primary">
                          <td className="px-3 py-2 text-white text-sm">{country.name}</td>
                          <td className="px-3 py-2 text-secondary text-sm">{country.count.toLocaleString()}</td>
                          <td className="px-3 py-2">
                            <div className="flex items-center">
                              <div className="w-16 bg-primary rounded-full h-2 mr-2">
                                <div
                                  className="bg-accent rounded-full h-2"
                                  style={{ width: `${percentage}%` }}
                                ></div>
                              </div>
                              <span className="text-secondary text-xs">{percentage.toFixed(1)}%</span>
                            </div>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Export Options */}
            <div className="bg-secondary rounded shadow p-4">
              <h2 className="text-lg font-bold text-white mb-4">Export Analytics</h2>

              <p className="text-secondary text-sm mb-4">
                Download your analytics data in various formats for further analysis.
              </p>

              <div className="flex flex-col space-y-2">
                <button className="bg-primary hover:bg-primary-light text-white px-3 py-2 rounded text-sm transition-colors flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                  </svg>
                  Export as CSV
                </button>

                <button className="bg-primary hover:bg-primary-light text-white px-3 py-2 rounded text-sm transition-colors flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                  </svg>
                  Export as JSON
                </button>

                <button className="bg-primary hover:bg-primary-light text-white px-3 py-2 rounded text-sm transition-colors flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                  </svg>
                  Export as PDF
                </button>
              </div>
            </div>
          </div>
        ) : null}
      </div>

      {/* Simple Footer */}
      <footer className="bg-secondary py-3 mt-8">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center">
            <div className="text-secondary">
              &copy; {new Date().getFullYear()} zkd.app
            </div>
            <div className="flex space-x-4">
              <Link to="/privacy" className="text-secondary hover:text-white transition-colors">Privacy</Link>
              <Link to="/terms" className="text-secondary hover:text-white transition-colors">Terms</Link>
              <Link to="/contact" className="text-secondary hover:text-white transition-colors">Contact</Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default DomainAnalytics;
