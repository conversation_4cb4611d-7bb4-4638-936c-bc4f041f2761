import { useState, useEffect } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import domainService from '../services/domainService';

const DomainDetails = () => {
  const { domainId } = useParams();
  const { currentUser } = useAuth();
  const navigate = useNavigate();
  
  const [domain, setDomain] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [formData, setFormData] = useState({
    settings: {}
  });
  const [isSaving, setIsSaving] = useState(false);
  const [saveError, setSaveError] = useState(null);
  const [saveSuccess, setSaveSuccess] = useState(false);
  
  // Fetch domain details
  useEffect(() => {
    const fetchDomain = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const domainData = await domainService.getDomainById(domainId);
        setDomain(domainData);
        setFormData({
          ...domainData,
          settings: { ...domainData.settings }
        });
      } catch (err) {
        console.error('Error fetching domain:', err);
        setError('Failed to load domain details. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    if (domainId) {
      fetchDomain();
    }
  }, [domainId]);
  
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    if (name.startsWith('settings.')) {
      const settingName = name.replace('settings.', '');
      setFormData(prev => ({
        ...prev,
        settings: {
          ...prev.settings,
          [settingName]: type === 'checkbox' ? checked : value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? checked : value
      }));
    }
  };
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      setIsSaving(true);
      setSaveError(null);
      setSaveSuccess(false);
      
      // Update domain
      const updatedDomain = await domainService.updateDomain(domainId, formData);
      setDomain(updatedDomain);
      
      // Show success message
      setSaveSuccess(true);
      
      // Clear success message after 3 seconds
      setTimeout(() => {
        setSaveSuccess(false);
      }, 3000);
    } catch (err) {
      console.error('Error updating domain:', err);
      setSaveError('Failed to save changes. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };
  
  const handleDelete = async () => {
    if (!window.confirm('Are you sure you want to delete this domain? This action cannot be undone.')) {
      return;
    }
    
    try {
      await domainService.deleteDomain(domainId);
      navigate('/dashboard');
    } catch (error) {
      console.error('Error deleting domain:', error);
      alert('Failed to delete domain. Please try again.');
    }
  };
  
  return (
    <div className="bg-primary min-h-screen">
      {/* Simple Header/Navigation */}
      <header className="bg-secondary py-3 shadow-md">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center">
            <Link to="/" className="text-accent text-xl font-bold">ZKD.APP</Link>
            
            <nav className="flex items-center space-x-6">
              <Link to="/" className="text-white hover:text-accent transition-colors">Home</Link>
              <Link to="/dashboard" className="text-white hover:text-accent transition-colors">Dashboard</Link>
              <Link to="/pricing" className="text-white hover:text-accent transition-colors">Pricing</Link>
              <button 
                onClick={() => navigate(-1)} 
                className="text-white hover:text-accent transition-colors"
              >
                Back
              </button>
            </nav>
          </div>
        </div>
      </header>
      
      {/* Main Content */}
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-xl font-bold text-white">Domain Details</h1>
            {domain && (
              <p className="text-secondary">{domain.url}</p>
            )}
          </div>
          
          <div className="flex space-x-3">
            <Link 
              to={`/domain/${domainId}/analytics`}
              className="bg-primary hover:bg-primary-light text-white px-3 py-1 rounded text-sm transition-colors flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              Analytics
            </Link>
            
            <Link 
              to={`/domain/${domainId}/dns`}
              className="bg-primary hover:bg-primary-light text-white px-3 py-1 rounded text-sm transition-colors flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 10-5.656-5.656l-4 4a4 4 0 105.656 5.656l1.102-1.101" />
              </svg>
              DNS Settings
            </Link>
          </div>
        </div>
        
        {loading ? (
          <div className="flex justify-center items-center py-8">
            <div className="inline-block w-6 h-6 border-2 border-transparent border-t-accent rounded-full animate-spin"></div>
            <span className="ml-2 text-secondary">Loading domain details...</span>
          </div>
        ) : error ? (
          <div className="bg-red-900 bg-opacity-20 text-red-400 p-4 rounded text-center">
            {error}
            <button 
              className="ml-2 underline"
              onClick={() => window.location.reload()}
            >
              Retry
            </button>
          </div>
        ) : domain ? (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Domain Info */}
            <div className="md:col-span-1">
              <div className="bg-secondary rounded shadow p-4 mb-4">
                <h2 className="text-lg font-bold text-white mb-4">Domain Information</h2>
                
                <div className="mb-3">
                  <p className="text-secondary text-xs mb-1">Domain URL</p>
                  <p className="text-white">{domain.url}</p>
                </div>
                
                <div className="mb-3">
                  <p className="text-secondary text-xs mb-1">Type</p>
                  <p className="text-white">
                    <span className={`px-2 py-0.5 rounded text-xs ${domain.type === 'personal' ? 'bg-blue-900 text-blue-200' : 'bg-purple-900 text-purple-200'}`}>
                      {domain.type === 'personal' ? 'Personal' : 'Project'}
                    </span>
                  </p>
                </div>
                
                <div className="mb-3">
                  <p className="text-secondary text-xs mb-1">Status</p>
                  <p className="text-white">
                    <span className="px-2 py-0.5 rounded bg-green-900 text-green-200 text-xs">
                      {domain.status}
                    </span>
                  </p>
                </div>
                
                <div className="mb-3">
                  <p className="text-secondary text-xs mb-1">Created</p>
                  <p className="text-white">{domain.createdAt}</p>
                </div>
                
                <div className="mb-3">
                  <p className="text-secondary text-xs mb-1">Total Visits</p>
                  <p className="text-white">{domain.visits.toLocaleString()}</p>
                </div>
              </div>
              
              <div className="bg-secondary rounded shadow p-4">
                <h2 className="text-lg font-bold text-white mb-4">Danger Zone</h2>
                
                <button 
                  onClick={handleDelete}
                  className="w-full bg-red-800 hover:bg-red-700 text-white px-3 py-2 rounded transition-colors"
                >
                  Delete Domain
                </button>
                
                <p className="text-secondary text-xs mt-2">
                  This action cannot be undone. This will permanently delete the domain and all associated data.
                </p>
              </div>
            </div>
            
            {/* Domain Settings */}
            <div className="md:col-span-2">
              <div className="bg-secondary rounded shadow p-4">
                <h2 className="text-lg font-bold text-white mb-4">Domain Settings</h2>
                
                {saveSuccess && (
                  <div className="bg-green-900 bg-opacity-20 text-green-400 p-3 rounded mb-4">
                    Settings saved successfully!
                  </div>
                )}
                
                {saveError && (
                  <div className="bg-red-900 bg-opacity-20 text-red-400 p-3 rounded mb-4">
                    {saveError}
                  </div>
                )}
                
                <form onSubmit={handleSubmit}>
                  {domain.type === 'personal' ? (
                    <>
                      <div className="mb-4">
                        <label className="block text-secondary text-sm mb-2">Redirect URL</label>
                        <input
                          type="url"
                          name="settings.redirectUrl"
                          value={formData.settings.redirectUrl || ''}
                          onChange={handleChange}
                          className="w-full bg-primary text-white px-3 py-2 rounded border border-primary"
                          placeholder="https://github.com/yourusername"
                        />
                        <p className="text-secondary text-xs mt-1">
                          Where should your personal domain redirect to?
                        </p>
                      </div>
                      
                      <div className="mb-4">
                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            name="settings.showSocial"
                            checked={formData.settings.showSocial || false}
                            onChange={handleChange}
                            className="mr-2"
                          />
                          <span className="text-white text-sm">Show social links</span>
                        </label>
                        <p className="text-secondary text-xs mt-1 ml-5">
                          Display your social media links on your personal page
                        </p>
                      </div>
                      
                      <div className="mb-4">
                        <label className="block text-secondary text-sm mb-2">Theme</label>
                        <select
                          name="settings.theme"
                          value={formData.settings.theme || 'dark'}
                          onChange={handleChange}
                          className="w-full bg-primary text-white px-3 py-2 rounded border border-primary"
                        >
                          <option value="dark">Dark</option>
                          <option value="light">Light</option>
                          <option value="cyberpunk">Cyberpunk</option>
                          <option value="minimal">Minimal</option>
                        </select>
                      </div>
                    </>
                  ) : (
                    <>
                      <div className="mb-4">
                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            name="settings.customDns"
                            checked={formData.settings.customDns || false}
                            onChange={handleChange}
                            className="mr-2"
                          />
                          <span className="text-white text-sm">Enable custom DNS</span>
                        </label>
                        <p className="text-secondary text-xs mt-1 ml-5">
                          Allows you to configure custom DNS records for this domain
                        </p>
                      </div>
                      
                      <div className="mb-4">
                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            name="settings.sslEnabled"
                            checked={formData.settings.sslEnabled || false}
                            onChange={handleChange}
                            className="mr-2"
                          />
                          <span className="text-white text-sm">Enable SSL</span>
                        </label>
                        <p className="text-secondary text-xs mt-1 ml-5">
                          Secure your domain with HTTPS
                        </p>
                      </div>
                      
                      <div className="mb-4">
                        <label className="block text-secondary text-sm mb-2">Custom Header</label>
                        <textarea
                          name="settings.customHeader"
                          value={formData.settings.customHeader || ''}
                          onChange={handleChange}
                          className="w-full bg-primary text-white px-3 py-2 rounded border border-primary h-24"
                          placeholder="<!-- Add custom HTML for the header -->"
                        ></textarea>
                        <p className="text-secondary text-xs mt-1">
                          Custom HTML to include in the <code>&lt;head&gt;</code> section
                        </p>
                      </div>
                    </>
                  )}
                  
                  <div className="flex justify-end">
                    <button
                      type="submit"
                      className="bg-accent hover:bg-accent-dark text-white px-4 py-2 rounded transition-colors"
                      disabled={isSaving}
                    >
                      {isSaving ? (
                        <span className="flex items-center">
                          <span className="inline-block w-4 h-4 border-2 border-transparent border-t-white rounded-full animate-spin mr-2"></span>
                          Saving...
                        </span>
                      ) : 'Save Changes'}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        ) : null}
      </div>
      
      {/* Simple Footer */}
      <footer className="bg-secondary py-3 mt-8">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center">
            <div className="text-secondary">
              &copy; {new Date().getFullYear()} zkd.app
            </div>
            <div className="flex space-x-4">
              <Link to="/privacy" className="text-secondary hover:text-white transition-colors">Privacy</Link>
              <Link to="/terms" className="text-secondary hover:text-white transition-colors">Terms</Link>
              <Link to="/contact" className="text-secondary hover:text-white transition-colors">Contact</Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default DomainDetails;
