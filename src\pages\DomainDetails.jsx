import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, Link, useNavigate } from 'react-router-dom'

const DomainDetails = () => {
  const { id } = useParams()
  const navigate = useNavigate()
  const [domain, setDomain] = useState(null)
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('settings')

  useEffect(() => {
    // Mock domain data
    setTimeout(() => {
      setDomain({
        id,
        name: 'johndoe.zkd.app',
        type: 'Personal',
        status: 'Active',
        visits: 1234,
        created: '2024-01-15',
        settings: {
          redirectUrl: 'https://github.com/johndoe',
          description: 'My personal website',
          isPublic: true
        }
      })
      setLoading(false)
    }, 500)
  }, [id])

  const handleSave = () => {
    // Mock save functionality
    alert('Settings saved successfully!')
  }

  const handleDelete = () => {
    if (window.confirm('Are you sure you want to delete this domain? This action cannot be undone.')) {
      // Mock delete functionality
      navigate('/dashboard')
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="loading mr-2"></div>
        <span>Loading domain...</span>
      </div>
    )
  }

  if (!domain) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Domain Not Found</h1>
          <Link to="/dashboard" className="btn btn-primary">
            Back to Dashboard
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen py-8">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold">{domain.name}</h1>
            <p className="text-slate-400 mt-2">{domain.type} Domain</p>
          </div>
          <Link to="/dashboard" className="btn btn-secondary">
            Back to Dashboard
          </Link>
        </div>

        {/* Tabs */}
        <div className="flex space-x-1 mb-8">
          <button
            onClick={() => setActiveTab('settings')}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              activeTab === 'settings' 
                ? 'bg-blue-600 text-white' 
                : 'bg-slate-700 text-slate-300 hover:bg-slate-600'
            }`}
          >
            Settings
          </button>
          <button
            onClick={() => setActiveTab('analytics')}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              activeTab === 'analytics' 
                ? 'bg-blue-600 text-white' 
                : 'bg-slate-700 text-slate-300 hover:bg-slate-600'
            }`}
          >
            Analytics
          </button>
          <button
            onClick={() => setActiveTab('dns')}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              activeTab === 'dns' 
                ? 'bg-blue-600 text-white' 
                : 'bg-slate-700 text-slate-300 hover:bg-slate-600'
            }`}
          >
            DNS
          </button>
        </div>

        {/* Tab Content */}
        {activeTab === 'settings' && (
          <div className="card">
            <h2 className="text-xl font-bold mb-6">Domain Settings</h2>
            
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium mb-2">Redirect URL</label>
                <input
                  type="url"
                  defaultValue={domain.settings.redirectUrl}
                  className="input"
                  placeholder="https://example.com"
                />
                <p className="text-slate-400 text-sm mt-1">
                  Where visitors will be redirected when they visit your domain
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Description</label>
                <textarea
                  defaultValue={domain.settings.description}
                  className="input"
                  rows="3"
                  placeholder="Describe your domain..."
                />
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isPublic"
                  defaultChecked={domain.settings.isPublic}
                  className="mr-2"
                />
                <label htmlFor="isPublic" className="text-sm">
                  Make this domain publicly visible in directory
                </label>
              </div>

              <div className="flex space-x-4">
                <button onClick={handleSave} className="btn btn-primary">
                  Save Changes
                </button>
                <button onClick={handleDelete} className="btn btn-danger">
                  Delete Domain
                </button>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'analytics' && (
          <div className="card">
            <h2 className="text-xl font-bold mb-6">Analytics</h2>
            
            <div className="grid md:grid-cols-3 gap-6 mb-8">
              <div className="bg-slate-700 p-4 rounded-lg">
                <h3 className="text-lg font-semibold mb-2">Total Visits</h3>
                <p className="text-3xl font-bold text-blue-400">{domain.visits.toLocaleString()}</p>
              </div>
              <div className="bg-slate-700 p-4 rounded-lg">
                <h3 className="text-lg font-semibold mb-2">This Month</h3>
                <p className="text-3xl font-bold text-green-400">234</p>
              </div>
              <div className="bg-slate-700 p-4 rounded-lg">
                <h3 className="text-lg font-semibold mb-2">Today</h3>
                <p className="text-3xl font-bold text-purple-400">12</p>
              </div>
            </div>

            <p className="text-slate-400">
              Detailed analytics coming soon. Track your domain's performance and visitor statistics.
            </p>
          </div>
        )}

        {activeTab === 'dns' && (
          <div className="card">
            <h2 className="text-xl font-bold mb-6">DNS Management</h2>
            
            <div className="space-y-4">
              <div className="bg-slate-700 p-4 rounded-lg">
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="font-semibold">A Record</h3>
                    <p className="text-slate-400 text-sm">@ → 192.168.1.1</p>
                  </div>
                  <button className="text-blue-400 hover:text-blue-300 text-sm">
                    Edit
                  </button>
                </div>
              </div>
              
              <div className="bg-slate-700 p-4 rounded-lg">
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="font-semibold">CNAME Record</h3>
                    <p className="text-slate-400 text-sm">www → @</p>
                  </div>
                  <button className="text-blue-400 hover:text-blue-300 text-sm">
                    Edit
                  </button>
                </div>
              </div>
            </div>

            <button className="btn btn-primary mt-6">
              Add DNS Record
            </button>
          </div>
        )}
      </div>
    </div>
  )
}

export default DomainDetails
