"use strict";var je=Object.create;var fe=Object.defineProperty;var Me=Object.getOwnPropertyDescriptor;var We=Object.getOwnPropertyNames;var Be=Object.getPrototypeOf,He=Object.prototype.hasOwnProperty;var se=(e,o)=>(o=Symbol[e])?o:Symbol.for("Symbol."+e),ue=e=>{throw TypeError(e)};var qe=(e,o,r,t)=>{if(o&&typeof o=="object"||typeof o=="function")for(let n of We(o))!He.call(e,n)&&n!==r&&fe(e,n,{get:()=>o[n],enumerable:!(t=Me(o,n))||t.enumerable});return e};var _=(e,o,r)=>(r=e!=null?je(Be(e)):{},qe(o||!e||!e.__esModule?fe(r,"default",{value:e,enumerable:!0}):r,e));var ce=(e,o,r)=>{if(o!=null){typeof o!="object"&&typeof o!="function"&&ue("Object expected");var t,n;r&&(t=o[se("asyncDispose")]),t===void 0&&(t=o[se("dispose")],r&&(n=t)),typeof t!="function"&&ue("Object not disposable"),n&&(t=function(){try{n.call(this)}catch(i){return Promise.reject(i)}}),e.push([r,t,o])}else r&&e.push([r]);return o},pe=(e,o,r)=>{var t=typeof SuppressedError=="function"?SuppressedError:function(l,s,a,f){return f=Error(a),f.name="SuppressedError",f.error=l,f.suppressed=s,f},n=l=>o=r?new t(l,o,"An error was suppressed during disposal"):(r=!0,l),i=l=>{for(;l=e.pop();)try{var s=l[1]&&l[1].call(l[2]);if(l[0])return Promise.resolve(s).then(i,a=>(n(a),i()))}catch(a){n(a)}if(r)throw o};return i()};var _e=_(require("@alloc/quick-lru")),m=require("@tailwindcss/node"),Ke=require("@tailwindcss/node/require-cache"),Ue=require("@tailwindcss/oxide"),De=_(require("fs")),w=_(require("path")),G=_(require("postcss"));function J(e){return{kind:"word",value:e}}function Ge(e,o){return{kind:"function",value:e,nodes:o}}function Ye(e){return{kind:"separator",value:e}}function K(e,o,r=null){for(let t=0;t<e.length;t++){let n=e[t],i=!1,l=0,s=o(n,{parent:r,replaceWith(a){i||(i=!0,Array.isArray(a)?a.length===0?(e.splice(t,1),l=0):a.length===1?(e[t]=a[0],l=1):(e.splice(t,1,...a),l=a.length):e[t]=a)}})??0;if(i){s===0?t--:t+=l-1;continue}if(s===2)return 2;if(s!==1&&n.kind==="function"&&K(n.nodes,o,n)===2)return 2}}function E(e){let o="";for(let r of e)switch(r.kind){case"word":case"separator":{o+=r.value;break}case"function":o+=r.value+"("+E(r.nodes)+")"}return o}var de=92,Je=41,me=58,ge=44,Qe=34,he=61,ve=62,we=60,ke=10,Ze=40,Xe=39,ye=47,be=32,xe=9;function R(e){e=e.replaceAll(`\r
`,`
`);let o=[],r=[],t=null,n="",i;for(let l=0;l<e.length;l++){let s=e.charCodeAt(l);switch(s){case de:{n+=e[l]+e[l+1],l++;break}case me:case ge:case he:case ve:case we:case ke:case ye:case be:case xe:{if(n.length>0){let b=J(n);t?t.nodes.push(b):o.push(b),n=""}let a=l,f=l+1;for(;f<e.length&&(i=e.charCodeAt(f),!(i!==me&&i!==ge&&i!==he&&i!==ve&&i!==we&&i!==ke&&i!==ye&&i!==be&&i!==xe));f++);l=f-1;let V=Ye(e.slice(a,f));t?t.nodes.push(V):o.push(V);break}case Xe:case Qe:{let a=l;for(let f=l+1;f<e.length;f++)if(i=e.charCodeAt(f),i===de)f+=1;else if(i===s){l=f;break}n+=e.slice(a,l+1);break}case Ze:{let a=Ge(n,[]);n="",t?t.nodes.push(a):o.push(a),r.push(a),t=a;break}case Je:{let a=r.pop();if(n.length>0){let f=J(n);a.nodes.push(f),n=""}r.length>0?t=r[r.length-1]:t=null;break}default:n+=String.fromCharCode(s)}}return n.length>0&&o.push(J(n)),o}var et=["anchor-size"],_t=new RegExp(`(${et.join("|")})\\(`,"g");var It=new Uint8Array(256);var W=new Uint8Array(256);function g(e,o){let r=0,t=[],n=0,i=e.length,l=o.charCodeAt(0);for(let s=0;s<i;s++){let a=e.charCodeAt(s);if(r===0&&a===l){t.push(e.slice(n,s)),n=s+1;continue}switch(a){case 92:s+=1;break;case 39:case 34:for(;++s<i;){let f=e.charCodeAt(s);if(f===92){s+=1;continue}if(f===a)break}break;case 40:W[r]=41,r++;break;case 91:W[r]=93,r++;break;case 123:W[r]=125,r++;break;case 93:case 125:case 41:r>0&&a===W[r-1]&&r--;break}}return t.push(e.slice(n)),t}var C=/[+-]?\d*\.?\d+(?:[eE][+-]?\d+)?/,rr=new RegExp(`^${C.source}$`);var ir=new RegExp(`^${C.source}%$`);var nr=new RegExp(`^${C.source}s*/s*${C.source}$`);var tt=["cm","mm","Q","in","pc","pt","px","em","ex","ch","rem","lh","rlh","vw","vh","vmin","vmax","vb","vi","svw","svh","lvw","lvh","dvw","dvh","cqw","cqh","cqi","cqb","cqmin","cqmax"],or=new RegExp(`^${C.source}(${tt.join("|")})$`);var rt=["deg","rad","grad","turn"],lr=new RegExp(`^${C.source}(${rt.join("|")})$`);var ar=new RegExp(`^${C.source} +${C.source} +${C.source}$`);function v(e){let o=Number(e);return Number.isInteger(o)&&o>=0&&String(o)===String(e)}function F(e,o){if(o===null)return e;let r=Number(o);return Number.isNaN(r)||(o=`${r*100}%`),o==="100%"?e:`color-mix(in oklab, ${e} ${o}, transparent)`}var ot={"--alpha":lt,"--spacing":at,"--theme":st,theme:ut};function lt(e,o,r,...t){let[n,i]=g(r,"/").map(l=>l.trim());if(!n||!i)throw new Error(`The --alpha(\u2026) function requires a color and an alpha value, e.g.: \`--alpha(${n||"var(--my-color)"} / ${i||"50%"})\``);if(t.length>0)throw new Error(`The --alpha(\u2026) function only accepts one argument, e.g.: \`--alpha(${n||"var(--my-color)"} / ${i||"50%"})\``);return F(n,i)}function at(e,o,r,...t){if(!r)throw new Error("The --spacing(\u2026) function requires an argument, but received none.");if(t.length>0)throw new Error(`The --spacing(\u2026) function only accepts a single argument, but received ${t.length+1}.`);let n=e.theme.resolve(null,["--spacing"]);if(!n)throw new Error("The --spacing(\u2026) function requires that the `--spacing` theme variable exists, but it was not found.");return`calc(${n} * ${r})`}function st(e,o,r,...t){if(!r.startsWith("--"))throw new Error("The --theme(\u2026) function can only be used with CSS variables from your theme.");let n=!1;r.endsWith(" inline")&&(n=!0,r=r.slice(0,-7)),o.kind==="at-rule"&&(n=!0);let i=e.resolveThemeValue(r,n);if(!i){if(t.length>0)return t.join(", ");throw new Error(`Could not resolve value for theme function: \`theme(${r})\`. Consider checking if the variable name is correct or provide a fallback value to silence this error.`)}if(t.length===0)return i;let l=t.join(", ");if(l==="initial")return i;if(i==="initial")return l;if(i.startsWith("var(")||i.startsWith("theme(")||i.startsWith("--theme(")){let s=R(i);return ct(s,l),E(s)}return i}function ut(e,o,r,...t){r=ft(r);let n=e.resolveThemeValue(r);if(!n&&t.length>0)return t.join(", ");if(!n)throw new Error(`Could not resolve value for theme function: \`theme(${r})\`. Consider checking if the path is correct or provide a fallback value to silence this error.`);return n}var Sr=new RegExp(Object.keys(ot).map(e=>`${e}\\(`).join("|"));function ft(e){if(e[0]!=="'"&&e[0]!=='"')return e;let o="",r=e[0];for(let t=1;t<e.length-1;t++){let n=e[t],i=e[t+1];n==="\\"&&(i===r||i==="\\")?(o+=i,t++):o+=n}return o}function ct(e,o){K(e,r=>{if(r.kind==="function"&&!(r.value!=="var"&&r.value!=="theme"&&r.value!=="--theme"))if(r.nodes.length===1)r.nodes.push({kind:"word",value:`, ${o}`});else{let t=r.nodes[r.nodes.length-1];t.kind==="word"&&t.value==="initial"&&(t.value=o)}})}var vt=32;var wt=40;function $e(e,o=[]){for(let r=5;r<e.length;r++){let t=e.charCodeAt(r);if(t===vt||t===wt){let n=e.slice(0,r).trim(),i=e.slice(r).trim();return h(n,i,o)}}return h(e.trim(),"",o)}var te={inherit:"inherit",current:"currentcolor",transparent:"transparent",black:"#000",white:"#fff",slate:{50:"oklch(98.4% 0.003 247.858)",100:"oklch(96.8% 0.007 247.896)",200:"oklch(92.9% 0.013 255.508)",300:"oklch(86.9% 0.022 252.894)",400:"oklch(70.4% 0.04 256.788)",500:"oklch(55.4% 0.046 257.417)",600:"oklch(44.6% 0.043 257.281)",700:"oklch(37.2% 0.044 257.287)",800:"oklch(27.9% 0.041 260.031)",900:"oklch(20.8% 0.042 265.755)",950:"oklch(12.9% 0.042 264.695)"},gray:{50:"oklch(98.5% 0.002 247.839)",100:"oklch(96.7% 0.003 264.542)",200:"oklch(92.8% 0.006 264.531)",300:"oklch(87.2% 0.01 258.338)",400:"oklch(70.7% 0.022 261.325)",500:"oklch(55.1% 0.027 264.364)",600:"oklch(44.6% 0.03 256.802)",700:"oklch(37.3% 0.034 259.733)",800:"oklch(27.8% 0.033 256.848)",900:"oklch(21% 0.034 264.665)",950:"oklch(13% 0.028 261.692)"},zinc:{50:"oklch(98.5% 0 0)",100:"oklch(96.7% 0.001 286.375)",200:"oklch(92% 0.004 286.32)",300:"oklch(87.1% 0.006 286.286)",400:"oklch(70.5% 0.015 286.067)",500:"oklch(55.2% 0.016 285.938)",600:"oklch(44.2% 0.017 285.786)",700:"oklch(37% 0.013 285.805)",800:"oklch(27.4% 0.006 286.033)",900:"oklch(21% 0.006 285.885)",950:"oklch(14.1% 0.005 285.823)"},neutral:{50:"oklch(98.5% 0 0)",100:"oklch(97% 0 0)",200:"oklch(92.2% 0 0)",300:"oklch(87% 0 0)",400:"oklch(70.8% 0 0)",500:"oklch(55.6% 0 0)",600:"oklch(43.9% 0 0)",700:"oklch(37.1% 0 0)",800:"oklch(26.9% 0 0)",900:"oklch(20.5% 0 0)",950:"oklch(14.5% 0 0)"},stone:{50:"oklch(98.5% 0.001 106.423)",100:"oklch(97% 0.001 106.424)",200:"oklch(92.3% 0.003 48.717)",300:"oklch(86.9% 0.005 56.366)",400:"oklch(70.9% 0.01 56.259)",500:"oklch(55.3% 0.013 58.071)",600:"oklch(44.4% 0.011 73.639)",700:"oklch(37.4% 0.01 67.558)",800:"oklch(26.8% 0.007 34.298)",900:"oklch(21.6% 0.006 56.043)",950:"oklch(14.7% 0.004 49.25)"},red:{50:"oklch(97.1% 0.013 17.38)",100:"oklch(93.6% 0.032 17.717)",200:"oklch(88.5% 0.062 18.334)",300:"oklch(80.8% 0.114 19.571)",400:"oklch(70.4% 0.191 22.216)",500:"oklch(63.7% 0.237 25.331)",600:"oklch(57.7% 0.245 27.325)",700:"oklch(50.5% 0.213 27.518)",800:"oklch(44.4% 0.177 26.899)",900:"oklch(39.6% 0.141 25.723)",950:"oklch(25.8% 0.092 26.042)"},orange:{50:"oklch(98% 0.016 73.684)",100:"oklch(95.4% 0.038 75.164)",200:"oklch(90.1% 0.076 70.697)",300:"oklch(83.7% 0.128 66.29)",400:"oklch(75% 0.183 55.934)",500:"oklch(70.5% 0.213 47.604)",600:"oklch(64.6% 0.222 41.116)",700:"oklch(55.3% 0.195 38.402)",800:"oklch(47% 0.157 37.304)",900:"oklch(40.8% 0.123 38.172)",950:"oklch(26.6% 0.079 36.259)"},amber:{50:"oklch(98.7% 0.022 95.277)",100:"oklch(96.2% 0.059 95.617)",200:"oklch(92.4% 0.12 95.746)",300:"oklch(87.9% 0.169 91.605)",400:"oklch(82.8% 0.189 84.429)",500:"oklch(76.9% 0.188 70.08)",600:"oklch(66.6% 0.179 58.318)",700:"oklch(55.5% 0.163 48.998)",800:"oklch(47.3% 0.137 46.201)",900:"oklch(41.4% 0.112 45.904)",950:"oklch(27.9% 0.077 45.635)"},yellow:{50:"oklch(98.7% 0.026 102.212)",100:"oklch(97.3% 0.071 103.193)",200:"oklch(94.5% 0.129 101.54)",300:"oklch(90.5% 0.182 98.111)",400:"oklch(85.2% 0.199 91.936)",500:"oklch(79.5% 0.184 86.047)",600:"oklch(68.1% 0.162 75.834)",700:"oklch(55.4% 0.135 66.442)",800:"oklch(47.6% 0.114 61.907)",900:"oklch(42.1% 0.095 57.708)",950:"oklch(28.6% 0.066 53.813)"},lime:{50:"oklch(98.6% 0.031 120.757)",100:"oklch(96.7% 0.067 122.328)",200:"oklch(93.8% 0.127 124.321)",300:"oklch(89.7% 0.196 126.665)",400:"oklch(84.1% 0.238 128.85)",500:"oklch(76.8% 0.233 130.85)",600:"oklch(64.8% 0.2 131.684)",700:"oklch(53.2% 0.157 131.589)",800:"oklch(45.3% 0.124 130.933)",900:"oklch(40.5% 0.101 131.063)",950:"oklch(27.4% 0.072 132.109)"},green:{50:"oklch(98.2% 0.018 155.826)",100:"oklch(96.2% 0.044 156.743)",200:"oklch(92.5% 0.084 155.995)",300:"oklch(87.1% 0.15 154.449)",400:"oklch(79.2% 0.209 151.711)",500:"oklch(72.3% 0.219 149.579)",600:"oklch(62.7% 0.194 149.214)",700:"oklch(52.7% 0.154 150.069)",800:"oklch(44.8% 0.119 151.328)",900:"oklch(39.3% 0.095 152.535)",950:"oklch(26.6% 0.065 152.934)"},emerald:{50:"oklch(97.9% 0.021 166.113)",100:"oklch(95% 0.052 163.051)",200:"oklch(90.5% 0.093 164.15)",300:"oklch(84.5% 0.143 164.978)",400:"oklch(76.5% 0.177 163.223)",500:"oklch(69.6% 0.17 162.48)",600:"oklch(59.6% 0.145 163.225)",700:"oklch(50.8% 0.118 165.612)",800:"oklch(43.2% 0.095 166.913)",900:"oklch(37.8% 0.077 168.94)",950:"oklch(26.2% 0.051 172.552)"},teal:{50:"oklch(98.4% 0.014 180.72)",100:"oklch(95.3% 0.051 180.801)",200:"oklch(91% 0.096 180.426)",300:"oklch(85.5% 0.138 181.071)",400:"oklch(77.7% 0.152 181.912)",500:"oklch(70.4% 0.14 182.503)",600:"oklch(60% 0.118 184.704)",700:"oklch(51.1% 0.096 186.391)",800:"oklch(43.7% 0.078 188.216)",900:"oklch(38.6% 0.063 188.416)",950:"oklch(27.7% 0.046 192.524)"},cyan:{50:"oklch(98.4% 0.019 200.873)",100:"oklch(95.6% 0.045 203.388)",200:"oklch(91.7% 0.08 205.041)",300:"oklch(86.5% 0.127 207.078)",400:"oklch(78.9% 0.154 211.53)",500:"oklch(71.5% 0.143 215.221)",600:"oklch(60.9% 0.126 221.723)",700:"oklch(52% 0.105 223.128)",800:"oklch(45% 0.085 224.283)",900:"oklch(39.8% 0.07 227.392)",950:"oklch(30.2% 0.056 229.695)"},sky:{50:"oklch(97.7% 0.013 236.62)",100:"oklch(95.1% 0.026 236.824)",200:"oklch(90.1% 0.058 230.902)",300:"oklch(82.8% 0.111 230.318)",400:"oklch(74.6% 0.16 232.661)",500:"oklch(68.5% 0.169 237.323)",600:"oklch(58.8% 0.158 241.966)",700:"oklch(50% 0.134 242.749)",800:"oklch(44.3% 0.11 240.79)",900:"oklch(39.1% 0.09 240.876)",950:"oklch(29.3% 0.066 243.157)"},blue:{50:"oklch(97% 0.014 254.604)",100:"oklch(93.2% 0.032 255.585)",200:"oklch(88.2% 0.059 254.128)",300:"oklch(80.9% 0.105 251.813)",400:"oklch(70.7% 0.165 254.624)",500:"oklch(62.3% 0.214 259.815)",600:"oklch(54.6% 0.245 262.881)",700:"oklch(48.8% 0.243 264.376)",800:"oklch(42.4% 0.199 265.638)",900:"oklch(37.9% 0.146 265.522)",950:"oklch(28.2% 0.091 267.935)"},indigo:{50:"oklch(96.2% 0.018 272.314)",100:"oklch(93% 0.034 272.788)",200:"oklch(87% 0.065 274.039)",300:"oklch(78.5% 0.115 274.713)",400:"oklch(67.3% 0.182 276.935)",500:"oklch(58.5% 0.233 277.117)",600:"oklch(51.1% 0.262 276.966)",700:"oklch(45.7% 0.24 277.023)",800:"oklch(39.8% 0.195 277.366)",900:"oklch(35.9% 0.144 278.697)",950:"oklch(25.7% 0.09 281.288)"},violet:{50:"oklch(96.9% 0.016 293.756)",100:"oklch(94.3% 0.029 294.588)",200:"oklch(89.4% 0.057 293.283)",300:"oklch(81.1% 0.111 293.571)",400:"oklch(70.2% 0.183 293.541)",500:"oklch(60.6% 0.25 292.717)",600:"oklch(54.1% 0.281 293.009)",700:"oklch(49.1% 0.27 292.581)",800:"oklch(43.2% 0.232 292.759)",900:"oklch(38% 0.189 293.745)",950:"oklch(28.3% 0.141 291.089)"},purple:{50:"oklch(97.7% 0.014 308.299)",100:"oklch(94.6% 0.033 307.174)",200:"oklch(90.2% 0.063 306.703)",300:"oklch(82.7% 0.119 306.383)",400:"oklch(71.4% 0.203 305.504)",500:"oklch(62.7% 0.265 303.9)",600:"oklch(55.8% 0.288 302.321)",700:"oklch(49.6% 0.265 301.924)",800:"oklch(43.8% 0.218 303.724)",900:"oklch(38.1% 0.176 304.987)",950:"oklch(29.1% 0.149 302.717)"},fuchsia:{50:"oklch(97.7% 0.017 320.058)",100:"oklch(95.2% 0.037 318.852)",200:"oklch(90.3% 0.076 319.62)",300:"oklch(83.3% 0.145 321.434)",400:"oklch(74% 0.238 322.16)",500:"oklch(66.7% 0.295 322.15)",600:"oklch(59.1% 0.293 322.896)",700:"oklch(51.8% 0.253 323.949)",800:"oklch(45.2% 0.211 324.591)",900:"oklch(40.1% 0.17 325.612)",950:"oklch(29.3% 0.136 325.661)"},pink:{50:"oklch(97.1% 0.014 343.198)",100:"oklch(94.8% 0.028 342.258)",200:"oklch(89.9% 0.061 343.231)",300:"oklch(82.3% 0.12 346.018)",400:"oklch(71.8% 0.202 349.761)",500:"oklch(65.6% 0.241 354.308)",600:"oklch(59.2% 0.249 0.584)",700:"oklch(52.5% 0.223 3.958)",800:"oklch(45.9% 0.187 3.815)",900:"oklch(40.8% 0.153 2.432)",950:"oklch(28.4% 0.109 3.907)"},rose:{50:"oklch(96.9% 0.015 12.422)",100:"oklch(94.1% 0.03 12.58)",200:"oklch(89.2% 0.058 10.001)",300:"oklch(81% 0.117 11.638)",400:"oklch(71.2% 0.194 13.428)",500:"oklch(64.5% 0.246 16.439)",600:"oklch(58.6% 0.253 17.585)",700:"oklch(51.4% 0.222 16.935)",800:"oklch(45.5% 0.188 13.697)",900:"oklch(41% 0.159 10.272)",950:"oklch(27.1% 0.105 12.094)"}};function O(e){return{__BARE_VALUE__:e}}var y=O(e=>{if(v(e.value))return e.value}),d=O(e=>{if(v(e.value))return`${e.value}%`}),T=O(e=>{if(v(e.value))return`${e.value}px`}),Ee=O(e=>{if(v(e.value))return`${e.value}ms`}),q=O(e=>{if(v(e.value))return`${e.value}deg`}),St=O(e=>{if(e.fraction===null)return;let[o,r]=g(e.fraction,"/");if(!(!v(o)||!v(r)))return e.fraction}),Re=O(e=>{if(v(Number(e.value)))return`repeat(${e.value}, minmax(0, 1fr))`}),$t={accentColor:({theme:e})=>e("colors"),animation:{none:"none",spin:"spin 1s linear infinite",ping:"ping 1s cubic-bezier(0, 0, 0.2, 1) infinite",pulse:"pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite",bounce:"bounce 1s infinite"},aria:{busy:'busy="true"',checked:'checked="true"',disabled:'disabled="true"',expanded:'expanded="true"',hidden:'hidden="true"',pressed:'pressed="true"',readonly:'readonly="true"',required:'required="true"',selected:'selected="true"'},aspectRatio:{auto:"auto",square:"1 / 1",video:"16 / 9",...St},backdropBlur:({theme:e})=>e("blur"),backdropBrightness:({theme:e})=>({...e("brightness"),...d}),backdropContrast:({theme:e})=>({...e("contrast"),...d}),backdropGrayscale:({theme:e})=>({...e("grayscale"),...d}),backdropHueRotate:({theme:e})=>({...e("hueRotate"),...q}),backdropInvert:({theme:e})=>({...e("invert"),...d}),backdropOpacity:({theme:e})=>({...e("opacity"),...d}),backdropSaturate:({theme:e})=>({...e("saturate"),...d}),backdropSepia:({theme:e})=>({...e("sepia"),...d}),backgroundColor:({theme:e})=>e("colors"),backgroundImage:{none:"none","gradient-to-t":"linear-gradient(to top, var(--tw-gradient-stops))","gradient-to-tr":"linear-gradient(to top right, var(--tw-gradient-stops))","gradient-to-r":"linear-gradient(to right, var(--tw-gradient-stops))","gradient-to-br":"linear-gradient(to bottom right, var(--tw-gradient-stops))","gradient-to-b":"linear-gradient(to bottom, var(--tw-gradient-stops))","gradient-to-bl":"linear-gradient(to bottom left, var(--tw-gradient-stops))","gradient-to-l":"linear-gradient(to left, var(--tw-gradient-stops))","gradient-to-tl":"linear-gradient(to top left, var(--tw-gradient-stops))"},backgroundOpacity:({theme:e})=>e("opacity"),backgroundPosition:{bottom:"bottom",center:"center",left:"left","left-bottom":"left bottom","left-top":"left top",right:"right","right-bottom":"right bottom","right-top":"right top",top:"top"},backgroundSize:{auto:"auto",cover:"cover",contain:"contain"},blur:{0:"0",none:"",sm:"4px",DEFAULT:"8px",md:"12px",lg:"16px",xl:"24px","2xl":"40px","3xl":"64px"},borderColor:({theme:e})=>({DEFAULT:"currentcolor",...e("colors")}),borderOpacity:({theme:e})=>e("opacity"),borderRadius:{none:"0px",sm:"0.125rem",DEFAULT:"0.25rem",md:"0.375rem",lg:"0.5rem",xl:"0.75rem","2xl":"1rem","3xl":"1.5rem",full:"9999px"},borderSpacing:({theme:e})=>e("spacing"),borderWidth:{DEFAULT:"1px",0:"0px",2:"2px",4:"4px",8:"8px",...T},boxShadow:{sm:"0 1px 2px 0 rgb(0 0 0 / 0.05)",DEFAULT:"0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)",md:"0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)",lg:"0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)",xl:"0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)","2xl":"0 25px 50px -12px rgb(0 0 0 / 0.25)",inner:"inset 0 2px 4px 0 rgb(0 0 0 / 0.05)",none:"none"},boxShadowColor:({theme:e})=>e("colors"),brightness:{0:"0",50:".5",75:".75",90:".9",95:".95",100:"1",105:"1.05",110:"1.1",125:"1.25",150:"1.5",200:"2",...d},caretColor:({theme:e})=>e("colors"),colors:()=>({...te}),columns:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12","3xs":"16rem","2xs":"18rem",xs:"20rem",sm:"24rem",md:"28rem",lg:"32rem",xl:"36rem","2xl":"42rem","3xl":"48rem","4xl":"56rem","5xl":"64rem","6xl":"72rem","7xl":"80rem",...y},container:{},content:{none:"none"},contrast:{0:"0",50:".5",75:".75",100:"1",125:"1.25",150:"1.5",200:"2",...d},cursor:{auto:"auto",default:"default",pointer:"pointer",wait:"wait",text:"text",move:"move",help:"help","not-allowed":"not-allowed",none:"none","context-menu":"context-menu",progress:"progress",cell:"cell",crosshair:"crosshair","vertical-text":"vertical-text",alias:"alias",copy:"copy","no-drop":"no-drop",grab:"grab",grabbing:"grabbing","all-scroll":"all-scroll","col-resize":"col-resize","row-resize":"row-resize","n-resize":"n-resize","e-resize":"e-resize","s-resize":"s-resize","w-resize":"w-resize","ne-resize":"ne-resize","nw-resize":"nw-resize","se-resize":"se-resize","sw-resize":"sw-resize","ew-resize":"ew-resize","ns-resize":"ns-resize","nesw-resize":"nesw-resize","nwse-resize":"nwse-resize","zoom-in":"zoom-in","zoom-out":"zoom-out"},divideColor:({theme:e})=>e("borderColor"),divideOpacity:({theme:e})=>e("borderOpacity"),divideWidth:({theme:e})=>({...e("borderWidth"),...T}),dropShadow:{sm:"0 1px 1px rgb(0 0 0 / 0.05)",DEFAULT:["0 1px 2px rgb(0 0 0 / 0.1)","0 1px 1px rgb(0 0 0 / 0.06)"],md:["0 4px 3px rgb(0 0 0 / 0.07)","0 2px 2px rgb(0 0 0 / 0.06)"],lg:["0 10px 8px rgb(0 0 0 / 0.04)","0 4px 3px rgb(0 0 0 / 0.1)"],xl:["0 20px 13px rgb(0 0 0 / 0.03)","0 8px 5px rgb(0 0 0 / 0.08)"],"2xl":"0 25px 25px rgb(0 0 0 / 0.15)",none:"0 0 #0000"},fill:({theme:e})=>e("colors"),flex:{1:"1 1 0%",auto:"1 1 auto",initial:"0 1 auto",none:"none"},flexBasis:({theme:e})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",...e("spacing")}),flexGrow:{0:"0",DEFAULT:"1",...y},flexShrink:{0:"0",DEFAULT:"1",...y},fontFamily:{sans:["ui-sans-serif","system-ui","sans-serif",'"Apple Color Emoji"','"Segoe UI Emoji"','"Segoe UI Symbol"','"Noto Color Emoji"'],serif:["ui-serif","Georgia","Cambria",'"Times New Roman"',"Times","serif"],mono:["ui-monospace","SFMono-Regular","Menlo","Monaco","Consolas",'"Liberation Mono"','"Courier New"',"monospace"]},fontSize:{xs:["0.75rem",{lineHeight:"1rem"}],sm:["0.875rem",{lineHeight:"1.25rem"}],base:["1rem",{lineHeight:"1.5rem"}],lg:["1.125rem",{lineHeight:"1.75rem"}],xl:["1.25rem",{lineHeight:"1.75rem"}],"2xl":["1.5rem",{lineHeight:"2rem"}],"3xl":["1.875rem",{lineHeight:"2.25rem"}],"4xl":["2.25rem",{lineHeight:"2.5rem"}],"5xl":["3rem",{lineHeight:"1"}],"6xl":["3.75rem",{lineHeight:"1"}],"7xl":["4.5rem",{lineHeight:"1"}],"8xl":["6rem",{lineHeight:"1"}],"9xl":["8rem",{lineHeight:"1"}]},fontWeight:{thin:"100",extralight:"200",light:"300",normal:"400",medium:"500",semibold:"600",bold:"700",extrabold:"800",black:"900"},gap:({theme:e})=>e("spacing"),gradientColorStops:({theme:e})=>e("colors"),gradientColorStopPositions:{"0%":"0%","5%":"5%","10%":"10%","15%":"15%","20%":"20%","25%":"25%","30%":"30%","35%":"35%","40%":"40%","45%":"45%","50%":"50%","55%":"55%","60%":"60%","65%":"65%","70%":"70%","75%":"75%","80%":"80%","85%":"85%","90%":"90%","95%":"95%","100%":"100%",...d},grayscale:{0:"0",DEFAULT:"100%",...d},gridAutoColumns:{auto:"auto",min:"min-content",max:"max-content",fr:"minmax(0, 1fr)"},gridAutoRows:{auto:"auto",min:"min-content",max:"max-content",fr:"minmax(0, 1fr)"},gridColumn:{auto:"auto","span-1":"span 1 / span 1","span-2":"span 2 / span 2","span-3":"span 3 / span 3","span-4":"span 4 / span 4","span-5":"span 5 / span 5","span-6":"span 6 / span 6","span-7":"span 7 / span 7","span-8":"span 8 / span 8","span-9":"span 9 / span 9","span-10":"span 10 / span 10","span-11":"span 11 / span 11","span-12":"span 12 / span 12","span-full":"1 / -1"},gridColumnEnd:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...y},gridColumnStart:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...y},gridRow:{auto:"auto","span-1":"span 1 / span 1","span-2":"span 2 / span 2","span-3":"span 3 / span 3","span-4":"span 4 / span 4","span-5":"span 5 / span 5","span-6":"span 6 / span 6","span-7":"span 7 / span 7","span-8":"span 8 / span 8","span-9":"span 9 / span 9","span-10":"span 10 / span 10","span-11":"span 11 / span 11","span-12":"span 12 / span 12","span-full":"1 / -1"},gridRowEnd:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...y},gridRowStart:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...y},gridTemplateColumns:{none:"none",subgrid:"subgrid",1:"repeat(1, minmax(0, 1fr))",2:"repeat(2, minmax(0, 1fr))",3:"repeat(3, minmax(0, 1fr))",4:"repeat(4, minmax(0, 1fr))",5:"repeat(5, minmax(0, 1fr))",6:"repeat(6, minmax(0, 1fr))",7:"repeat(7, minmax(0, 1fr))",8:"repeat(8, minmax(0, 1fr))",9:"repeat(9, minmax(0, 1fr))",10:"repeat(10, minmax(0, 1fr))",11:"repeat(11, minmax(0, 1fr))",12:"repeat(12, minmax(0, 1fr))",...Re},gridTemplateRows:{none:"none",subgrid:"subgrid",1:"repeat(1, minmax(0, 1fr))",2:"repeat(2, minmax(0, 1fr))",3:"repeat(3, minmax(0, 1fr))",4:"repeat(4, minmax(0, 1fr))",5:"repeat(5, minmax(0, 1fr))",6:"repeat(6, minmax(0, 1fr))",7:"repeat(7, minmax(0, 1fr))",8:"repeat(8, minmax(0, 1fr))",9:"repeat(9, minmax(0, 1fr))",10:"repeat(10, minmax(0, 1fr))",11:"repeat(11, minmax(0, 1fr))",12:"repeat(12, minmax(0, 1fr))",...Re},height:({theme:e})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%",full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),hueRotate:{0:"0deg",15:"15deg",30:"30deg",60:"60deg",90:"90deg",180:"180deg",...q},inset:({theme:e})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%",full:"100%",...e("spacing")}),invert:{0:"0",DEFAULT:"100%",...d},keyframes:{spin:{to:{transform:"rotate(360deg)"}},ping:{"75%, 100%":{transform:"scale(2)",opacity:"0"}},pulse:{"50%":{opacity:".5"}},bounce:{"0%, 100%":{transform:"translateY(-25%)",animationTimingFunction:"cubic-bezier(0.8,0,1,1)"},"50%":{transform:"none",animationTimingFunction:"cubic-bezier(0,0,0.2,1)"}}},letterSpacing:{tighter:"-0.05em",tight:"-0.025em",normal:"0em",wide:"0.025em",wider:"0.05em",widest:"0.1em"},lineHeight:{none:"1",tight:"1.25",snug:"1.375",normal:"1.5",relaxed:"1.625",loose:"2",3:".75rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem"},listStyleType:{none:"none",disc:"disc",decimal:"decimal"},listStyleImage:{none:"none"},margin:({theme:e})=>({auto:"auto",...e("spacing")}),lineClamp:{1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",...y},maxHeight:({theme:e})=>({none:"none",full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),maxWidth:({theme:e})=>({none:"none",xs:"20rem",sm:"24rem",md:"28rem",lg:"32rem",xl:"36rem","2xl":"42rem","3xl":"48rem","4xl":"56rem","5xl":"64rem","6xl":"72rem","7xl":"80rem",full:"100%",min:"min-content",max:"max-content",fit:"fit-content",prose:"65ch",...e("spacing")}),minHeight:({theme:e})=>({full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),minWidth:({theme:e})=>({full:"100%",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),objectPosition:{bottom:"bottom",center:"center",left:"left","left-bottom":"left bottom","left-top":"left top",right:"right","right-bottom":"right bottom","right-top":"right top",top:"top"},opacity:{0:"0",5:"0.05",10:"0.1",15:"0.15",20:"0.2",25:"0.25",30:"0.3",35:"0.35",40:"0.4",45:"0.45",50:"0.5",55:"0.55",60:"0.6",65:"0.65",70:"0.7",75:"0.75",80:"0.8",85:"0.85",90:"0.9",95:"0.95",100:"1",...d},order:{first:"-9999",last:"9999",none:"0",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",...y},outlineColor:({theme:e})=>e("colors"),outlineOffset:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...T},outlineWidth:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...T},padding:({theme:e})=>e("spacing"),placeholderColor:({theme:e})=>e("colors"),placeholderOpacity:({theme:e})=>e("opacity"),ringColor:({theme:e})=>({DEFAULT:"currentcolor",...e("colors")}),ringOffsetColor:({theme:e})=>e("colors"),ringOffsetWidth:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...T},ringOpacity:({theme:e})=>({DEFAULT:"0.5",...e("opacity")}),ringWidth:{DEFAULT:"3px",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...T},rotate:{0:"0deg",1:"1deg",2:"2deg",3:"3deg",6:"6deg",12:"12deg",45:"45deg",90:"90deg",180:"180deg",...q},saturate:{0:"0",50:".5",100:"1",150:"1.5",200:"2",...d},scale:{0:"0",50:".5",75:".75",90:".9",95:".95",100:"1",105:"1.05",110:"1.1",125:"1.25",150:"1.5",...d},screens:{sm:"40rem",md:"48rem",lg:"64rem",xl:"80rem","2xl":"96rem"},scrollMargin:({theme:e})=>e("spacing"),scrollPadding:({theme:e})=>e("spacing"),sepia:{0:"0",DEFAULT:"100%",...d},skew:{0:"0deg",1:"1deg",2:"2deg",3:"3deg",6:"6deg",12:"12deg",...q},space:({theme:e})=>e("spacing"),spacing:{px:"1px",0:"0px",.5:"0.125rem",1:"0.25rem",1.5:"0.375rem",2:"0.5rem",2.5:"0.625rem",3:"0.75rem",3.5:"0.875rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem",11:"2.75rem",12:"3rem",14:"3.5rem",16:"4rem",20:"5rem",24:"6rem",28:"7rem",32:"8rem",36:"9rem",40:"10rem",44:"11rem",48:"12rem",52:"13rem",56:"14rem",60:"15rem",64:"16rem",72:"18rem",80:"20rem",96:"24rem"},stroke:({theme:e})=>({none:"none",...e("colors")}),strokeWidth:{0:"0",1:"1",2:"2",...y},supports:{},data:{},textColor:({theme:e})=>e("colors"),textDecorationColor:({theme:e})=>e("colors"),textDecorationThickness:{auto:"auto","from-font":"from-font",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...T},textIndent:({theme:e})=>e("spacing"),textOpacity:({theme:e})=>e("opacity"),textUnderlineOffset:{auto:"auto",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...T},transformOrigin:{center:"center",top:"top","top-right":"top right",right:"right","bottom-right":"bottom right",bottom:"bottom","bottom-left":"bottom left",left:"left","top-left":"top left"},transitionDelay:{0:"0s",75:"75ms",100:"100ms",150:"150ms",200:"200ms",300:"300ms",500:"500ms",700:"700ms",1e3:"1000ms",...Ee},transitionDuration:{DEFAULT:"150ms",0:"0s",75:"75ms",100:"100ms",150:"150ms",200:"200ms",300:"300ms",500:"500ms",700:"700ms",1e3:"1000ms",...Ee},transitionProperty:{none:"none",all:"all",DEFAULT:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter",colors:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke",opacity:"opacity",shadow:"box-shadow",transform:"transform"},transitionTimingFunction:{DEFAULT:"cubic-bezier(0.4, 0, 0.2, 1)",linear:"linear",in:"cubic-bezier(0.4, 0, 1, 1)",out:"cubic-bezier(0, 0, 0.2, 1)","in-out":"cubic-bezier(0.4, 0, 0.2, 1)"},translate:({theme:e})=>({"1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%",full:"100%",...e("spacing")}),size:({theme:e})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),width:({theme:e})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",screen:"100vw",svw:"100svw",lvw:"100lvw",dvw:"100dvw",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),willChange:{auto:"auto",scroll:"scroll-position",contents:"contents",transform:"transform"},zIndex:{auto:"auto",0:"0",10:"10",20:"20",30:"30",40:"40",50:"50",...y}};var Tt=64;function D(e,o=[]){return{kind:"rule",selector:e,nodes:o}}function h(e,o="",r=[]){return{kind:"at-rule",name:e,params:o,nodes:r}}function N(e,o=[]){return e.charCodeAt(0)===Tt?$e(e,o):D(e,o)}function A(e,o,r=!1){return{kind:"declaration",property:e,value:o,important:r}}function H(e){return{kind:"comment",value:e}}function z(e){function o(t,n=0){let i="",l="  ".repeat(n);if(t.kind==="declaration")i+=`${l}${t.property}: ${t.value}${t.important?" !important":""};
`;else if(t.kind==="rule"){i+=`${l}${t.selector} {
`;for(let s of t.nodes)i+=o(s,n+1);i+=`${l}}
`}else if(t.kind==="at-rule"){if(t.nodes.length===0)return`${l}${t.name} ${t.params};
`;i+=`${l}${t.name}${t.params?` ${t.params} `:" "}{
`;for(let s of t.nodes)i+=o(s,n+1);i+=`${l}}
`}else if(t.kind==="comment")i+=`${l}/*${t.value}*/
`;else if(t.kind==="context"||t.kind==="at-root")return"";return i}let r="";for(let t of e){let n=o(t);n!==""&&(r+=n)}return r}var I=_(require("postcss"));var Vt=33;function Pe(e,o){let r=I.default.root();r.source=o;function t(n,i){if(n.kind==="declaration"){let l=I.default.decl({prop:n.property,value:n.value??"",important:n.important});l.source=o,i.append(l)}else if(n.kind==="rule"){let l=I.default.rule({selector:n.selector});l.source=o,l.raws.semicolon=!0,i.append(l);for(let s of n.nodes)t(s,l)}else if(n.kind==="at-rule"){let l=I.default.atRule({name:n.name.slice(1),params:n.params});l.source=o,l.raws.semicolon=!0,i.append(l);for(let s of n.nodes)t(s,l)}else if(n.kind==="comment"){let l=I.default.comment({text:n.value});l.raws.left="",l.raws.right="",l.source=o,i.append(l)}else n.kind==="at-root"||n.kind}for(let n of e)t(n,r);return r}function Oe(e){function o(t,n){if(t.type==="decl")n.push(A(t.prop,t.value,t.important));else if(t.type==="rule"){let i=N(t.selector);t.each(l=>o(l,i.nodes)),n.push(i)}else if(t.type==="atrule"){let i=h(`@${t.name}`,t.params);t.each(l=>o(l,i.nodes)),n.push(i)}else if(t.type==="comment"){if(t.text.charCodeAt(0)!==Vt)return;n.push(H(t.text))}}let r=[];return e.each(t=>o(t,r)),r}var ne=require("@tailwindcss/node"),j=_(require("path")),re="'",ie='"';function oe(){let e=new WeakSet;function o(r){let t=r.root().source?.input.file;if(!t)return;let n=r.source?.input.file;if(!n||e.has(r))return;let i=r.params[0],l=i[0]===ie&&i[i.length-1]===ie?ie:i[0]===re&&i[i.length-1]===re?re:null;if(!l)return;let s=r.params.slice(1,-1),a="";if(s.startsWith("!")&&(s=s.slice(1),a="!"),!s.startsWith("./")&&!s.startsWith("../"))return;let f=j.default.posix.join((0,ne.normalizePath)(j.default.dirname(n)),s),V=j.default.posix.dirname((0,ne.normalizePath)(t)),b=j.default.posix.relative(V,f);b.startsWith(".")||(b="./"+b),r.params=l+a+b+l,e.add(r)}return{postcssPlugin:"tailwindcss-postcss-fix-relative-paths",Once(r){r.walkAtRules(/source|plugin|config/,o)}}}var u=m.env.DEBUG,le=new _e.default({maxSize:50});function Et(e,o){let r=`${e}:${o.base??""}:${JSON.stringify(o.optimize)}`;if(le.has(r))return le.get(r);let t={mtimes:new Map,compiler:null,scanner:null,tailwindCssAst:[],cachedPostCssAst:G.default.root(),optimizedPostCssAst:G.default.root(),fullRebuildPaths:[]};return le.set(r,t),t}function Rt(e={}){let o=e.base??process.cwd(),r=e.optimize??process.env.NODE_ENV==="production";return{postcssPlugin:"@tailwindcss/postcss",plugins:[oe(),{postcssPlugin:"tailwindcss",async Once(t,{result:n}){var ae=[];try{let i=ce(ae,new m.Instrumentation);let l=n.opts.from??"";let s=l.endsWith(".module.css");u&&i.start(`[@tailwindcss/postcss] ${(0,w.relative)(o,l)}`);{u&&i.start("Quick bail check");let k=!0;if(t.walkAtRules(c=>{if(c.name==="import"||c.name==="reference"||c.name==="theme"||c.name==="variant"||c.name==="config"||c.name==="plugin"||c.name==="apply"||c.name==="tailwind")return k=!1,!1}),k)return;u&&i.end("Quick bail check")}let a=Et(l,e);let f=w.default.dirname(w.default.resolve(l));let V=a.compiler===null;async function b(){u&&i.start("Setup compiler"),a.fullRebuildPaths.length>0&&!V&&(0,Ke.clearRequireCache)(a.fullRebuildPaths),a.fullRebuildPaths=[],u&&i.start("PostCSS AST -> Tailwind CSS AST");let k=Oe(t);u&&i.end("PostCSS AST -> Tailwind CSS AST"),u&&i.start("Create compiler");let c=await(0,m.compileAst)(k,{base:f,shouldRewriteUrls:!0,onDependency:Y=>a.fullRebuildPaths.push(Y),polyfills:s?m.Polyfills.All^m.Polyfills.AtProperty:m.Polyfills.All});return u&&i.end("Create compiler"),u&&i.end("Setup compiler"),c}try{if(a.compiler??=b(),(await a.compiler).features===m.Features.None)return;let k="incremental";u&&i.start("Register full rebuild paths");{for(let p of a.fullRebuildPaths)n.messages.push({type:"dependency",plugin:"@tailwindcss/postcss",file:w.default.resolve(p),parent:n.opts.from});let S=n.messages.flatMap(p=>p.type!=="dependency"?[]:p.file);S.push(l);for(let p of S){let x=De.default.statSync(p,{throwIfNoEntry:!1})?.mtimeMs??null;if(x===null){p===l&&(k="full");continue}a.mtimes.get(p)!==x&&(k="full",a.mtimes.set(p,x))}}u&&i.end("Register full rebuild paths"),k==="full"&&!V&&(a.compiler=b());let c=await a.compiler;if(a.scanner===null||k==="full"){u&&i.start("Setup scanner");let S=(c.root==="none"?[]:c.root===null?[{base:o,pattern:"**/*",negated:!1}]:[{...c.root,negated:!1}]).concat(c.sources);a.scanner=new Ue.Scanner({sources:S}),u&&i.end("Setup scanner")}u&&i.start("Scan for candidates");let Y=c.features&m.Features.Utilities?a.scanner.scan():[];if(u&&i.end("Scan for candidates"),c.features&m.Features.Utilities){u&&i.start("Register dependency messages");let S=w.default.resolve(o,l);for(let p of a.scanner.files){let x=w.default.resolve(p);x!==S&&n.messages.push({type:"dependency",plugin:"@tailwindcss/postcss",file:x,parent:n.opts.from})}for(let{base:p,pattern:x}of a.scanner.globs)x==="*"&&o===p||(x===""?n.messages.push({type:"dependency",plugin:"@tailwindcss/postcss",file:w.default.resolve(p),parent:n.opts.from}):n.messages.push({type:"dir-dependency",plugin:"@tailwindcss/postcss",dir:w.default.resolve(p),glob:x,parent:n.opts.from}));u&&i.end("Register dependency messages")}u&&i.start("Build utilities");let M=c.build(Y);if(u&&i.end("Build utilities"),a.tailwindCssAst!==M)if(r){u&&i.start("Optimization"),u&&i.start("AST -> CSS");let S=z(M);u&&i.end("AST -> CSS"),u&&i.start("Lightning CSS");let p=(0,m.optimize)(S,{minify:typeof r=="object"?r.minify:!0});u&&i.end("Lightning CSS"),u&&i.start("CSS -> PostCSS AST"),a.optimizedPostCssAst=G.default.parse(p,n.opts),u&&i.end("CSS -> PostCSS AST"),u&&i.end("Optimization")}else u&&i.start("Transform Tailwind CSS AST into PostCSS AST"),a.cachedPostCssAst=Pe(M,t.source),u&&i.end("Transform Tailwind CSS AST into PostCSS AST");a.tailwindCssAst=M,u&&i.start("Update PostCSS AST"),t.removeAll(),t.append(r?a.optimizedPostCssAst.clone().nodes:a.cachedPostCssAst.clone().nodes),t.raws.indent="  ",u&&i.end("Update PostCSS AST"),u&&i.end(`[@tailwindcss/postcss] ${(0,w.relative)(o,l)}`)}catch(k){a.compiler=null;for(let c of a.fullRebuildPaths)n.messages.push({type:"dependency",plugin:"@tailwindcss/postcss",file:w.default.resolve(c),parent:n.opts.from});console.error(k),t.removeAll()}}catch(Ie){var Fe=Ie,Le=!0}finally{pe(ae,Fe,Le)}}}]}}var ze=Object.assign(Rt,{postcss:!0});module.exports=ze;
