import { useState, useEffect } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import domainService from '../services/domainService';
import dnsService from '../services/dnsService';

const DnsMgmt = () => {
  const { domainId } = useParams();
  const { currentUser } = useAuth();
  const navigate = useNavigate();
  
  const [domain, setDomain] = useState(null);
  const [dnsRecords, setDnsRecords] = useState([]);
  const [templates, setTemplates] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Form state
  const [showAddForm, setShowAddForm] = useState(false);
  const [showTemplateForm, setShowTemplateForm] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState('');
  const [formData, setFormData] = useState({
    type: 'A',
    name: '',
    value: '',
    ttl: 3600,
    priority: 10
  });
  const [formErrors, setFormErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [submitError, setSubmitError] = useState(null);
  
  // Fetch domain and DNS records
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Fetch domain details
        const domainData = await domainService.getDomainById(domainId);
        setDomain(domainData);
        
        // Fetch DNS records
        const records = await dnsService.getDomainRecords(domainId);
        setDnsRecords(records);
        
        // Fetch DNS templates
        const templateData = await dnsService.getDnsTemplates();
        setTemplates(templateData);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load DNS data. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    if (domainId) {
      fetchData();
    }
  }, [domainId]);
  
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'ttl' || name === 'priority' ? parseInt(value) : value
    }));
    
    // Clear error for this field
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: null
      }));
    }
  };
  
  const validateForm = () => {
    const result = dnsService.validateDnsRecord(formData);
    setFormErrors(result.errors);
    return result.isValid;
  };
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    try {
      setIsSubmitting(true);
      setSubmitError(null);
      setSubmitSuccess(false);
      
      // Add DNS record
      const newRecord = await dnsService.addDnsRecord(domainId, formData);
      
      // Update records list
      setDnsRecords(prev => [...prev, newRecord]);
      
      // Reset form
      setFormData({
        type: 'A',
        name: '',
        value: '',
        ttl: 3600,
        priority: 10
      });
      
      // Show success message
      setSubmitSuccess(true);
      
      // Hide form after successful submission
      setTimeout(() => {
        setShowAddForm(false);
        setSubmitSuccess(false);
      }, 2000);
    } catch (err) {
      console.error('Error adding DNS record:', err);
      setSubmitError(err.message || 'Failed to add DNS record. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };
  
  const handleApplyTemplate = async () => {
    if (!selectedTemplate) {
      return;
    }
    
    try {
      setIsSubmitting(true);
      setSubmitError(null);
      
      // Apply template
      const newRecords = await dnsService.applyDnsTemplate(domainId, selectedTemplate);
      
      // Update records list
      setDnsRecords(prev => [...prev, ...newRecords]);
      
      // Reset form
      setSelectedTemplate('');
      
      // Hide form after successful submission
      setShowTemplateForm(false);
    } catch (err) {
      console.error('Error applying DNS template:', err);
      setSubmitError(err.message || 'Failed to apply DNS template. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };
  
  const handleDeleteRecord = async (recordId) => {
    if (!window.confirm('Are you sure you want to delete this DNS record?')) {
      return;
    }
    
    try {
      await dnsService.deleteDnsRecord(domainId, recordId);
      
      // Update records list
      setDnsRecords(prev => prev.filter(record => record.id !== recordId));
    } catch (err) {
      console.error('Error deleting DNS record:', err);
      alert('Failed to delete DNS record. Please try again.');
    }
  };
  
  return (
    <div className="bg-primary min-h-screen">
      {/* Simple Header/Navigation */}
      <header className="bg-secondary py-3 shadow-md">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center">
            <Link to="/" className="text-accent text-xl font-bold">ZKD.APP</Link>
            
            <nav className="flex items-center space-x-6">
              <Link to="/" className="text-white hover:text-accent transition-colors">Home</Link>
              <Link to="/dashboard" className="text-white hover:text-accent transition-colors">Dashboard</Link>
              <Link to="/pricing" className="text-white hover:text-accent transition-colors">Pricing</Link>
              <button 
                onClick={() => navigate(-1)} 
                className="text-white hover:text-accent transition-colors"
              >
                Back
              </button>
            </nav>
          </div>
        </div>
      </header>
      
      {/* Main Content */}
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-xl font-bold text-white">DNS Management</h1>
            {domain && (
              <p className="text-secondary">{domain.url}</p>
            )}
          </div>
          
          <div className="flex space-x-3">
            <Link 
              to={`/domain/${domainId}/edit`}
              className="bg-primary hover:bg-primary-light text-white px-3 py-1 rounded text-sm transition-colors flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
              Edit Domain
            </Link>
            
            <Link 
              to={`/domain/${domainId}/analytics`}
              className="bg-primary hover:bg-primary-light text-white px-3 py-1 rounded text-sm transition-colors flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              Analytics
            </Link>
          </div>
        </div>
        
        {loading ? (
          <div className="flex justify-center items-center py-8">
            <div className="inline-block w-6 h-6 border-2 border-transparent border-t-accent rounded-full animate-spin"></div>
            <span className="ml-2 text-secondary">Loading DNS records...</span>
          </div>
        ) : error ? (
          <div className="bg-red-900 bg-opacity-20 text-red-400 p-4 rounded text-center">
            {error}
            <button 
              className="ml-2 underline"
              onClick={() => window.location.reload()}
            >
              Retry
            </button>
          </div>
        ) : (
          <>
            {/* DNS Records */}
            <div className="bg-secondary rounded shadow mb-6">
              <div className="p-4 border-b border-primary flex flex-col md:flex-row md:items-center md:justify-between">
                <h2 className="text-lg font-bold text-white mb-3 md:mb-0">DNS Records</h2>
                
                <div className="flex flex-col md:flex-row gap-3">
                  <button 
                    onClick={() => {
                      setShowAddForm(true);
                      setShowTemplateForm(false);
                    }}
                    className="bg-accent hover:bg-accent-dark text-white px-3 py-1 rounded text-sm flex items-center justify-center transition-colors"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Add Record
                  </button>
                  
                  <button 
                    onClick={() => {
                      setShowTemplateForm(true);
                      setShowAddForm(false);
                    }}
                    className="bg-primary hover:bg-primary-light text-white px-3 py-1 rounded text-sm flex items-center justify-center transition-colors"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2" />
                    </svg>
                    Use Template
                  </button>
                </div>
              </div>
              
              {/* Add Record Form */}
              {showAddForm && (
                <div className="p-4 border-b border-primary bg-primary-light">
                  <h3 className="text-md font-bold text-white mb-3">Add DNS Record</h3>
                  
                  {submitSuccess && (
                    <div className="bg-green-900 bg-opacity-20 text-green-400 p-3 rounded mb-4">
                      DNS record added successfully!
                    </div>
                  )}
                  
                  {submitError && (
                    <div className="bg-red-900 bg-opacity-20 text-red-400 p-3 rounded mb-4">
                      {submitError}
                    </div>
                  )}
                  
                  <form onSubmit={handleSubmit} className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-secondary text-xs mb-1">Record Type</label>
                      <select
                        name="type"
                        value={formData.type}
                        onChange={handleChange}
                        className="bg-primary text-white px-3 py-1 rounded text-sm w-full"
                      >
                        <option value="A">A (Address)</option>
                        <option value="AAAA">AAAA (IPv6 Address)</option>
                        <option value="CNAME">CNAME (Canonical Name)</option>
                        <option value="MX">MX (Mail Exchange)</option>
                        <option value="TXT">TXT (Text)</option>
                        <option value="NS">NS (Name Server)</option>
                        <option value="SRV">SRV (Service)</option>
                        <option value="CAA">CAA (Certificate Authority)</option>
                      </select>
                      {formErrors.type && (
                        <p className="text-red-400 text-xs mt-1">{formErrors.type}</p>
                      )}
                    </div>
                    
                    <div>
                      <label className="block text-secondary text-xs mb-1">Name</label>
                      <input
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleChange}
                        placeholder="e.g. @ or www"
                        className="bg-primary text-white px-3 py-1 rounded text-sm w-full"
                      />
                      {formErrors.name && (
                        <p className="text-red-400 text-xs mt-1">{formErrors.name}</p>
                      )}
                    </div>
                    
                    <div>
                      <label className="block text-secondary text-xs mb-1">Value</label>
                      <input
                        type="text"
                        name="value"
                        value={formData.value}
                        onChange={handleChange}
                        placeholder={formData.type === 'A' ? 'e.g. 192.0.2.1' : 'e.g. example.com'}
                        className="bg-primary text-white px-3 py-1 rounded text-sm w-full"
                      />
                      {formErrors.value && (
                        <p className="text-red-400 text-xs mt-1">{formErrors.value}</p>
                      )}
                    </div>
                    
                    {formData.type === 'MX' && (
                      <div>
                        <label className="block text-secondary text-xs mb-1">Priority</label>
                        <input
                          type="number"
                          name="priority"
                          value={formData.priority}
                          onChange={handleChange}
                          min="0"
                          className="bg-primary text-white px-3 py-1 rounded text-sm w-full"
                        />
                        {formErrors.priority && (
                          <p className="text-red-400 text-xs mt-1">{formErrors.priority}</p>
                        )}
                      </div>
                    )}
                    
                    <div>
                      <label className="block text-secondary text-xs mb-1">TTL (seconds)</label>
                      <input
                        type="number"
                        name="ttl"
                        value={formData.ttl}
                        onChange={handleChange}
                        min="0"
                        className="bg-primary text-white px-3 py-1 rounded text-sm w-full"
                      />
                      {formErrors.ttl && (
                        <p className="text-red-400 text-xs mt-1">{formErrors.ttl}</p>
                      )}
                    </div>
                    
                    <div className="md:col-span-2 flex justify-end">
                      <button
                        type="button"
                        onClick={() => setShowAddForm(false)}
                        className="bg-primary hover:bg-primary-light text-white px-3 py-1 rounded text-sm mr-2"
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        className="bg-accent hover:bg-accent-dark text-white px-3 py-1 rounded text-sm"
                        disabled={isSubmitting}
                      >
                        {isSubmitting ? (
                          <span className="flex items-center">
                            <span className="inline-block w-4 h-4 border-2 border-transparent border-t-white rounded-full animate-spin mr-1"></span>
                            Adding...
                          </span>
                        ) : 'Add Record'}
                      </button>
                    </div>
                  </form>
                </div>
              )}
              
              {/* Template Form */}
              {showTemplateForm && (
                <div className="p-4 border-b border-primary bg-primary-light">
                  <h3 className="text-md font-bold text-white mb-3">Apply DNS Template</h3>
                  
                  {submitError && (
                    <div className="bg-red-900 bg-opacity-20 text-red-400 p-3 rounded mb-4">
                      {submitError}
                    </div>
                  )}
                  
                  <div className="mb-4">
                    <label className="block text-secondary text-xs mb-1">Select Template</label>
                    <select
                      value={selectedTemplate}
                      onChange={(e) => setSelectedTemplate(e.target.value)}
                      className="bg-primary text-white px-3 py-1 rounded text-sm w-full"
                    >
                      <option value="">-- Select a template --</option>
                      <option value="website">Website Hosting</option>
                      <option value="email">Email Service</option>
                      <option value="github">GitHub Pages</option>
                      <option value="vercel">Vercel Deployment</option>
                    </select>
                  </div>
                  
                  {selectedTemplate && (
                    <div className="mb-4">
                      <h4 className="text-white text-sm mb-2">Template Records:</h4>
                      <div className="bg-primary p-3 rounded">
                        <ul className="text-secondary text-xs">
                          {templates[selectedTemplate]?.map((record, index) => (
                            <li key={index} className="mb-1">
                              <span className="text-accent">{record.type}</span> {record.name} → {record.value} {record.priority ? `(Priority: ${record.priority})` : ''} (TTL: {record.ttl})
                              <p className="text-secondary ml-4">{record.description}</p>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  )}
                  
                  <div className="flex justify-end">
                    <button
                      type="button"
                      onClick={() => setShowTemplateForm(false)}
                      className="bg-primary hover:bg-primary-light text-white px-3 py-1 rounded text-sm mr-2"
                    >
                      Cancel
                    </button>
                    <button
                      type="button"
                      onClick={handleApplyTemplate}
                      className="bg-accent hover:bg-accent-dark text-white px-3 py-1 rounded text-sm"
                      disabled={!selectedTemplate || isSubmitting}
                    >
                      {isSubmitting ? (
                        <span className="flex items-center">
                          <span className="inline-block w-4 h-4 border-2 border-transparent border-t-white rounded-full animate-spin mr-1"></span>
                          Applying...
                        </span>
                      ) : 'Apply Template'}
                    </button>
                  </div>
                </div>
              )}
              
              {/* Records Table */}
              <div className="overflow-x-auto">
                {dnsRecords.length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-secondary mb-4">No DNS records found for this domain.</p>
                    <button 
                      onClick={() => setShowAddForm(true)}
                      className="bg-accent hover:bg-accent-dark text-white px-3 py-1 rounded text-sm transition-colors"
                    >
                      Add Your First Record
                    </button>
                  </div>
                ) : (
                  <table className="w-full">
                    <thead>
                      <tr className="text-left text-secondary text-xs border-b border-primary">
                        <th className="px-4 py-2">Type</th>
                        <th className="px-4 py-2">Name</th>
                        <th className="px-4 py-2">Value</th>
                        <th className="px-4 py-2">TTL</th>
                        <th className="px-4 py-2">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {dnsRecords.map(record => (
                        <tr key={record.id} className="border-b border-primary hover:bg-primary-light transition-colors">
                          <td className="px-4 py-3">
                            <span className={`px-2 py-0.5 rounded text-xs ${
                              record.type === 'A' ? 'bg-blue-900 text-blue-200' : 
                              record.type === 'CNAME' ? 'bg-green-900 text-green-200' : 
                              record.type === 'MX' ? 'bg-purple-900 text-purple-200' : 
                              record.type === 'TXT' ? 'bg-yellow-900 text-yellow-200' : 
                              'bg-gray-900 text-gray-200'
                            }`}>
                              {record.type}
                            </span>
                          </td>
                          <td className="px-4 py-3 text-white text-sm">
                            {record.name}
                          </td>
                          <td className="px-4 py-3 text-white text-sm">
                            <div className="max-w-xs truncate">
                              {record.value}
                              {record.type === 'MX' && record.priority !== undefined && (
                                <span className="text-secondary text-xs ml-1">(Priority: {record.priority})</span>
                              )}
                            </div>
                          </td>
                          <td className="px-4 py-3 text-secondary text-sm">
                            {record.ttl}
                          </td>
                          <td className="px-4 py-3">
                            <div className="flex space-x-1">
                              <button 
                                onClick={() => handleDeleteRecord(record.id)}
                                className="p-1 text-red-400 hover:text-red-300" 
                                title="Delete"
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                )}
              </div>
            </div>
            
            {/* DNS Help */}
            <div className="bg-secondary rounded shadow p-4">
              <h2 className="text-lg font-bold text-white mb-4">DNS Help</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-white text-md mb-2">Common DNS Record Types</h3>
                  <ul className="text-secondary text-sm space-y-2">
                    <li><span className="text-accent">A</span> - Maps a domain to an IPv4 address</li>
                    <li><span className="text-accent">AAAA</span> - Maps a domain to an IPv6 address</li>
                    <li><span className="text-accent">CNAME</span> - Maps a domain to another domain name</li>
                    <li><span className="text-accent">MX</span> - Specifies mail servers for the domain</li>
                    <li><span className="text-accent">TXT</span> - Stores text information (often for verification)</li>
                  </ul>
                </div>
                
                <div>
                  <h3 className="text-white text-md mb-2">Common Name Values</h3>
                  <ul className="text-secondary text-sm space-y-2">
                    <li><span className="text-accent">@</span> - Represents the root domain itself</li>
                    <li><span className="text-accent">www</span> - Common subdomain for websites</li>
                    <li><span className="text-accent">mail</span> - Common subdomain for mail servers</li>
                    <li><span className="text-accent">*</span> - Wildcard that matches any subdomain</li>
                  </ul>
                </div>
              </div>
              
              <div className="mt-4">
                <h3 className="text-white text-md mb-2">DNS Propagation</h3>
                <p className="text-secondary text-sm">
                  DNS changes can take up to 48 hours to propagate across the internet, although most changes are visible within a few hours.
                  The TTL (Time To Live) value determines how long DNS resolvers should cache the record before checking for updates.
                </p>
              </div>
            </div>
          </>
        )}
      </div>
      
      {/* Simple Footer */}
      <footer className="bg-secondary py-3 mt-8">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center">
            <div className="text-secondary">
              &copy; {new Date().getFullYear()} zkd.app
            </div>
            <div className="flex space-x-4">
              <Link to="/privacy" className="text-secondary hover:text-white transition-colors">Privacy</Link>
              <Link to="/terms" className="text-secondary hover:text-white transition-colors">Terms</Link>
              <Link to="/contact" className="text-secondary hover:text-white transition-colors">Contact</Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default DnsMgmt;
