// Domain Management Service
// This service handles all domain-related operations

import cpanelService from './cpanelService';

// Mock database for domains
let domains = [
  {
    id: 'dom_1',
    url: 'dyn.zkd.app/user123',
    type: 'personal',
    status: 'Active',
    createdAt: '2023-05-15',
    visits: 1245,
    owner: '1', // User ID
    settings: {
      redirectUrl: 'https://github.com/user123',
      showSocial: true,
      theme: 'dark'
    }
  },
  {
    id: 'dom_2',
    url: 'project.zkd.app',
    type: 'project',
    status: 'Active',
    createdAt: '2023-06-20',
    visits: 3567,
    owner: '1', // User ID
    settings: {
      customDns: true,
      sslEnabled: true
    }
  }
];

// Analytics data for domains
const analyticsData = {
  'dom_1': {
    dailyVisits: [12, 15, 8, 20, 18, 25, 22],
    referrers: [
      { source: 'Direct', count: 450 },
      { source: 'Google', count: 320 },
      { source: 'Twitter', count: 280 },
      { source: 'GitHub', count: 195 }
    ],
    countries: [
      { name: 'United States', count: 520 },
      { name: 'Germany', count: 210 },
      { name: 'United Kingdom', count: 180 },
      { name: 'Canada', count: 135 },
      { name: 'Other', count: 200 }
    ]
  },
  'dom_2': {
    dailyVisits: [35, 42, 38, 45, 50, 48, 55],
    referrers: [
      { source: 'Direct', count: 1200 },
      { source: 'Google', count: 950 },
      { source: 'Product Hunt', count: 720 },
      { source: 'Twitter', count: 450 },
      { source: 'Other', count: 247 }
    ],
    countries: [
      { name: 'United States', count: 1450 },
      { name: 'Germany', count: 620 },
      { name: 'United Kingdom', count: 480 },
      { name: 'Canada', count: 320 },
      { name: 'Japan', count: 280 },
      { name: 'Other', count: 417 }
    ]
  }
};

const domainService = {
  /**
   * Get all domains for a user
   * @param {string} userId - The user ID
   * @returns {Promise<Array>} - Array of domains
   */
  getUserDomains: async (userId) => {
    try {
      // In a real app, this would be an API call
      // For now, we'll filter our mock database
      const userDomains = domains.filter(domain => domain.owner === userId);
      return userDomains;
    } catch (error) {
      console.error('Error fetching user domains:', error);
      throw new Error('Failed to fetch domains');
    }
  },

  /**
   * Get a domain by ID
   * @param {string} domainId - The domain ID
   * @returns {Promise<Object>} - Domain object
   */
  getDomainById: async (domainId) => {
    try {
      const domain = domains.find(d => d.id === domainId);
      if (!domain) {
        throw new Error('Domain not found');
      }
      return domain;
    } catch (error) {
      console.error('Error fetching domain:', error);
      throw error;
    }
  },

  /**
   * Create a new domain
   * @param {Object} domainData - The domain data
   * @param {string} userId - The user ID
   * @returns {Promise<Object>} - The created domain
   */
  createDomain: async (domainData, userId) => {
    try {
      // Check domain availability with cPanel service
      const domainName = domainData.type === 'personal' ? domainData.username : domainData.projectName;
      const available = await cpanelService.checkSubdomainAvailability(domainName, domainData.type);
      
      if (!available) {
        throw new Error('Domain is not available');
      }
      
      // Register domain with cPanel
      const result = await cpanelService.registerDomain({
        ...domainData,
        owner: userId
      });
      
      if (!result.success) {
        throw new Error(result.message || 'Failed to register domain');
      }
      
      // Create domain in our database
      const newDomain = {
        id: 'dom_' + (domains.length + 1),
        url: domainData.type === 'personal' 
          ? `dyn.zkd.app/${domainData.username}`
          : `${domainData.projectName}.zkd.app`,
        type: domainData.type === 'personal' ? 'personal' : 'project',
        status: 'Active',
        createdAt: new Date().toISOString().split('T')[0],
        visits: 0,
        owner: userId,
        settings: domainData.type === 'personal'
          ? { redirectUrl: '', showSocial: true, theme: 'dark' }
          : { customDns: false, sslEnabled: true }
      };
      
      // Add to our mock database
      domains.push(newDomain);
      
      // Initialize analytics
      analyticsData[newDomain.id] = {
        dailyVisits: [0, 0, 0, 0, 0, 0, 0],
        referrers: [{ source: 'Direct', count: 0 }],
        countries: [{ name: 'Unknown', count: 0 }]
      };
      
      return newDomain;
    } catch (error) {
      console.error('Error creating domain:', error);
      throw error;
    }
  },

  /**
   * Update a domain
   * @param {string} domainId - The domain ID
   * @param {Object} updates - The updates to apply
   * @returns {Promise<Object>} - The updated domain
   */
  updateDomain: async (domainId, updates) => {
    try {
      const domainIndex = domains.findIndex(d => d.id === domainId);
      if (domainIndex === -1) {
        throw new Error('Domain not found');
      }
      
      // Update domain in our mock database
      domains[domainIndex] = {
        ...domains[domainIndex],
        ...updates,
        settings: {
          ...domains[domainIndex].settings,
          ...(updates.settings || {})
        }
      };
      
      return domains[domainIndex];
    } catch (error) {
      console.error('Error updating domain:', error);
      throw error;
    }
  },

  /**
   * Delete a domain
   * @param {string} domainId - The domain ID
   * @returns {Promise<boolean>} - Success status
   */
  deleteDomain: async (domainId) => {
    try {
      const domainIndex = domains.findIndex(d => d.id === domainId);
      if (domainIndex === -1) {
        throw new Error('Domain not found');
      }
      
      // Remove from our mock database
      domains = domains.filter(d => d.id !== domainId);
      
      // Remove analytics
      delete analyticsData[domainId];
      
      return true;
    } catch (error) {
      console.error('Error deleting domain:', error);
      throw error;
    }
  },

  /**
   * Get analytics for a domain
   * @param {string} domainId - The domain ID
   * @returns {Promise<Object>} - Analytics data
   */
  getDomainAnalytics: async (domainId) => {
    try {
      const analytics = analyticsData[domainId];
      if (!analytics) {
        throw new Error('Analytics not found for this domain');
      }
      
      return analytics;
    } catch (error) {
      console.error('Error fetching domain analytics:', error);
      throw error;
    }
  },

  /**
   * Record a visit to a domain
   * @param {string} domainId - The domain ID
   * @param {Object} visitData - Data about the visit
   * @returns {Promise<boolean>} - Success status
   */
  recordDomainVisit: async (domainId, visitData) => {
    try {
      const domain = domains.find(d => d.id === domainId);
      if (!domain) {
        throw new Error('Domain not found');
      }
      
      // Update visit count
      domain.visits += 1;
      
      // Update analytics
      const analytics = analyticsData[domainId];
      if (analytics) {
        // Update daily visits (last day)
        analytics.dailyVisits[analytics.dailyVisits.length - 1] += 1;
        
        // Update referrer
        const referrer = visitData.referrer || 'Direct';
        const referrerIndex = analytics.referrers.findIndex(r => r.source === referrer);
        if (referrerIndex !== -1) {
          analytics.referrers[referrerIndex].count += 1;
        } else {
          analytics.referrers.push({ source: referrer, count: 1 });
        }
        
        // Update country
        const country = visitData.country || 'Unknown';
        const countryIndex = analytics.countries.findIndex(c => c.name === country);
        if (countryIndex !== -1) {
          analytics.countries[countryIndex].count += 1;
        } else {
          analytics.countries.push({ name: country, count: 1 });
        }
      }
      
      return true;
    } catch (error) {
      console.error('Error recording domain visit:', error);
      throw error;
    }
  }
};

export default domainService;
