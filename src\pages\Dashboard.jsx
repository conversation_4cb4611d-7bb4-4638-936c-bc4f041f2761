import { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { useAuth } from '../context/AuthContext'

// Mock data for domains
const mockDomains = [
  {
    id: '1',
    name: 'johndoe.zkd.app',
    type: 'Personal',
    status: 'Active',
    visits: 1234,
    created: '2024-01-15'
  },
  {
    id: '2',
    name: 'myproject.zkd.app',
    type: 'Project',
    status: 'Active',
    visits: 5678,
    created: '2024-02-20'
  }
]

const Dashboard = () => {
  const { user } = useAuth()
  const [domains, setDomains] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Simulate loading domains
    setTimeout(() => {
      setDomains(mockDomains)
      setLoading(false)
    }, 1000)
  }, [])

  const handleDeleteDomain = (domainId) => {
    if (window.confirm('Are you sure you want to delete this domain?')) {
      setDomains(domains.filter(domain => domain.id !== domainId))
    }
  }

  return (
    <div className="min-h-screen py-8">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold">Dashboard</h1>
            <p className="text-slate-400 mt-2">Welcome back, {user?.name}!</p>
          </div>
          <Link to="/register" className="btn btn-primary">
            Register New Domain
          </Link>
        </div>

        {/* Stats Cards */}
        <div className="grid md:grid-cols-3 gap-6 mb-8">
          <div className="card">
            <h3 className="text-lg font-semibold mb-2">Total Domains</h3>
            <p className="text-3xl font-bold text-blue-400">{domains.length}</p>
          </div>
          <div className="card">
            <h3 className="text-lg font-semibold mb-2">Total Visits</h3>
            <p className="text-3xl font-bold text-green-400">
              {domains.reduce((sum, domain) => sum + domain.visits, 0).toLocaleString()}
            </p>
          </div>
          <div className="card">
            <h3 className="text-lg font-semibold mb-2">Active Domains</h3>
            <p className="text-3xl font-bold text-purple-400">
              {domains.filter(domain => domain.status === 'Active').length}
            </p>
          </div>
        </div>

        {/* Domains Table */}
        <div className="card">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-bold">Your Domains</h2>
          </div>

          {loading ? (
            <div className="flex justify-center items-center py-12">
              <div className="loading mr-2"></div>
              <span>Loading domains...</span>
            </div>
          ) : domains.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-slate-400 mb-4">No domains registered yet.</p>
              <Link to="/register" className="btn btn-primary">
                Register Your First Domain
              </Link>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-slate-700">
                    <th className="text-left py-3 px-4">Domain</th>
                    <th className="text-left py-3 px-4">Type</th>
                    <th className="text-left py-3 px-4">Status</th>
                    <th className="text-left py-3 px-4">Visits</th>
                    <th className="text-left py-3 px-4">Created</th>
                    <th className="text-left py-3 px-4">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {domains.map(domain => (
                    <tr key={domain.id} className="border-b border-slate-700 hover:bg-slate-700/50">
                      <td className="py-3 px-4">
                        <div className="font-medium">{domain.name}</div>
                      </td>
                      <td className="py-3 px-4">
                        <span className={`px-2 py-1 rounded text-xs ${
                          domain.type === 'Personal' ? 'bg-blue-900 text-blue-200' : 'bg-purple-900 text-purple-200'
                        }`}>
                          {domain.type}
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        <span className={`px-2 py-1 rounded text-xs ${
                          domain.status === 'Active' ? 'bg-green-900 text-green-200' : 'bg-red-900 text-red-200'
                        }`}>
                          {domain.status}
                        </span>
                      </td>
                      <td className="py-3 px-4 text-slate-400">
                        {domain.visits.toLocaleString()}
                      </td>
                      <td className="py-3 px-4 text-slate-400">
                        {domain.created}
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex space-x-2">
                          <Link 
                            to={`/domain/${domain.id}`}
                            className="text-blue-400 hover:text-blue-300 text-sm"
                          >
                            Manage
                          </Link>
                          <button 
                            onClick={() => handleDeleteDomain(domain.id)}
                            className="text-red-400 hover:text-red-300 text-sm"
                          >
                            Delete
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default Dashboard
