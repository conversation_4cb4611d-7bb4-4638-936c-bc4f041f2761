import { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

const Dashboard = () => {
  const { currentUser, logout } = useAuth();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('domains');

  // Mock data for domains
  const [domains, setDomains] = useState([
    {
      id: '1',
      name: 'myusername',
      type: 'personal',
      url: 'dyn.zkd.app/myusername',
      status: 'active',
      createdAt: '2023-05-15',
      visits: 1243
    },
    {
      id: '2',
      name: 'myproject',
      type: 'project',
      url: 'myproject.zkd.app',
      status: 'active',
      createdAt: '2023-06-22',
      visits: 5621
    }
  ]);

  // Mock data for DNS records
  const [dnsRecords, setDnsRecords] = useState({
    'myproject': [
      { type: 'A', name: '@', value: '*********', ttl: 3600 },
      { type: 'CNAME', name: 'www', value: 'myproject.zkd.app', ttl: 3600 },
      { type: 'MX', name: '@', value: 'mail.myproject.zkd.app', priority: 10, ttl: 3600 }
    ]
  });

  // Mock data for API keys
  const [apiKeys, setApiKeys] = useState([
    { id: 'key_1', name: 'Development', key: 'zkd_dev_a1b2c3d4e5f6', created: '2023-05-20', lastUsed: '2023-07-15' },
    { id: 'key_2', name: 'Production', key: 'zkd_prod_g7h8i9j0k1l2', created: '2023-06-01', lastUsed: '2023-07-18' }
  ]);

  const handleTabChange = (tab) => {
    setActiveTab(tab);
  };

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const addDnsRecord = (domainName) => {
    // In a real app, you would make an API call here
    // For now, we'll just update the state
    const newRecord = { type: 'TXT', name: '@', value: 'v=spf1 include:_spf.zkd.app ~all', ttl: 3600 };
    setDnsRecords(prev => ({
      ...prev,
      [domainName]: [...(prev[domainName] || []), newRecord]
    }));
  };

  const createApiKey = () => {
    // In a real app, you would make an API call here
    // For now, we'll just update the state
    const newKey = {
      id: 'key_' + (apiKeys.length + 1),
      name: 'New Key',
      key: 'zkd_new_' + Math.random().toString(36).substring(2, 10),
      created: new Date().toISOString().split('T')[0],
      lastUsed: 'Never'
    };
    setApiKeys(prev => [...prev, newKey]);
  };

  const deleteApiKey = (keyId) => {
    // In a real app, you would make an API call here
    // For now, we'll just update the state
    setApiKeys(prev => prev.filter(key => key.id !== keyId));
  };

  return (
    <div className="bg-primary min-h-screen">
      {/* Simple Header/Navigation */}
      <header className="bg-secondary py-3 shadow-md">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center">
            <Link to="/" className="text-accent text-xl font-bold">ZKD.APP</Link>

            <nav className="flex items-center space-x-6">
              <Link to="/" className="text-white hover:text-accent transition-colors">Home</Link>
              <Link to="/register" className="text-white hover:text-accent transition-colors">Register</Link>
              <Link to="/pricing" className="text-white hover:text-accent transition-colors">Pricing</Link>
              <Link to="/dashboard" className="text-accent font-medium">Dashboard</Link>
              <button
                onClick={handleLogout}
                className="bg-primary hover:bg-accent text-white px-3 py-1 rounded transition-colors"
              >
                Logout
              </button>
            </nav>
          </div>
        </div>
      </header>

      {/* Simple Tab Navigation */}
      <div className="container mx-auto px-4 py-4">
        <div className="flex border-b border-secondary mb-4">
          <button
            className={`px-4 py-2 font-medium ${activeTab === 'domains' ? 'text-accent border-b-2 border-accent' : 'text-white'}`}
            onClick={() => handleTabChange('domains')}
          >
            Domains
          </button>
          <button
            className={`px-4 py-2 font-medium ${activeTab === 'dns' ? 'text-accent border-b-2 border-accent' : 'text-white'}`}
            onClick={() => handleTabChange('dns')}
          >
            DNS
          </button>
          <button
            className={`px-4 py-2 font-medium ${activeTab === 'api' ? 'text-accent border-b-2 border-accent' : 'text-white'}`}
            onClick={() => handleTabChange('api')}
          >
            API Keys
          </button>
          <button
            className={`px-4 py-2 font-medium ${activeTab === 'settings' ? 'text-accent border-b-2 border-accent' : 'text-white'}`}
            onClick={() => handleTabChange('settings')}
          >
            Settings
          </button>
        </div>

        {/* Main Content */}
        <main>
          <div className="mb-4">
            <h1 className="text-xl font-bold text-white">Dashboard</h1>
            <p className="text-secondary text-sm">Manage your domains and account</p>
          </div>

          {/* Domains Tab */}
          <div className={`${activeTab === 'domains' ? 'block' : 'hidden'}`}>
            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div className="bg-secondary rounded shadow p-4">
                <div className="flex items-center">
                  <div className="bg-primary p-2 rounded-full mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-accent" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                    </svg>
                  </div>
                  <div>
                    <p className="text-secondary text-xs">Total Domains</p>
                    <h3 className="text-white text-lg font-bold">{domains.length}</h3>
                  </div>
                </div>
              </div>

              <div className="bg-secondary rounded shadow p-4">
                <div className="flex items-center">
                  <div className="bg-primary p-2 rounded-full mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-accent" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                  <div>
                    <p className="text-secondary text-xs">Personal Domains</p>
                    <h3 className="text-white text-lg font-bold">{domains.filter(d => d.type === 'personal').length}</h3>
                  </div>
                </div>
              </div>

              <div className="bg-secondary rounded shadow p-4">
                <div className="flex items-center">
                  <div className="bg-primary p-2 rounded-full mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-accent" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                  </div>
                  <div>
                    <p className="text-secondary text-xs">Project Domains</p>
                    <h3 className="text-white text-lg font-bold">{domains.filter(d => d.type === 'project').length}</h3>
                  </div>
                </div>
              </div>
            </div>

            {/* Domains List with Search and Filter */}
            <div className="bg-secondary rounded shadow mb-6">
              <div className="p-4 border-b border-primary">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                  <h2 className="text-lg font-bold text-white mb-3 md:mb-0">Your Domains</h2>

                  <div className="flex flex-col md:flex-row gap-3">
                    <div className="relative">
                      <input
                        type="text"
                        placeholder="Search domains..."
                        className="bg-primary text-white pl-8 pr-3 py-1 rounded text-sm w-full"
                      />
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-secondary absolute left-2 top-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                      </svg>
                    </div>

                    <Link
                      to="/register"
                      className="bg-accent hover:bg-accent-dark text-white px-3 py-1 rounded text-sm flex items-center justify-center transition-colors"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                      Register Domain
                    </Link>
                  </div>
                </div>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="text-left text-secondary text-xs border-b border-primary">
                      <th className="px-4 py-2">Domain</th>
                      <th className="px-4 py-2">Type</th>
                      <th className="px-4 py-2">Status</th>
                      <th className="px-4 py-2">Created</th>
                      <th className="px-4 py-2">Visits</th>
                      <th className="px-4 py-2">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {domains.map(domain => (
                      <tr key={domain.id} className="border-b border-primary hover:bg-primary-light transition-colors">
                        <td className="px-4 py-3">
                          <div className="text-white text-sm">{domain.url}</div>
                        </td>
                        <td className="px-4 py-3">
                          <span className={`px-2 py-0.5 rounded text-xs ${domain.type === 'personal' ? 'bg-blue-900 text-blue-200' : 'bg-purple-900 text-purple-200'}`}>
                            {domain.type === 'personal' ? 'Personal' : 'Project'}
                          </span>
                        </td>
                        <td className="px-4 py-3">
                          <span className="px-2 py-0.5 rounded bg-green-900 text-green-200 text-xs">
                            {domain.status}
                          </span>
                        </td>
                        <td className="px-4 py-3 text-secondary text-sm">
                          {domain.createdAt}
                        </td>
                        <td className="px-4 py-3 text-secondary text-sm">
                          {domain.visits.toLocaleString()}
                        </td>
                        <td className="px-4 py-3">
                          <div className="flex space-x-1">
                            <button className="p-1 text-blue-400 hover:text-blue-300" title="Edit">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                              </svg>
                            </button>
                            <button className="p-1 text-green-400 hover:text-green-300" title="Analytics">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                              </svg>
                            </button>
                            <button className="p-1 text-red-400 hover:text-red-300" title="Delete">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                              </svg>
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              <div className="p-3 border-t border-primary flex items-center justify-between">
                <div className="text-secondary text-xs">
                  Showing <span className="text-white">{domains.length}</span> domains
                </div>

                <div className="flex space-x-1">
                  <button className="px-2 py-0.5 rounded bg-primary text-white text-xs">1</button>
                  <button className="px-2 py-0.5 rounded hover:bg-primary text-secondary text-xs">2</button>
                  <button className="px-2 py-0.5 rounded hover:bg-primary text-secondary text-xs">3</button>
                </div>
              </div>
            </div>
          </div>

          {/* DNS Tab */}
          <div className={`${activeTab === 'dns' ? 'block' : 'hidden'}`}>
            <div className="bg-secondary rounded shadow mb-6">
              <div className="p-4 border-b border-primary">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                  <h2 className="text-lg font-bold text-white mb-3 md:mb-0">DNS Records</h2>

                  <div className="flex flex-col md:flex-row gap-3">
                    <select className="bg-primary text-white px-3 py-1 rounded text-sm">
                      <option value="myproject">myproject.zkd.app</option>
                      <option value="myusername">dyn.zkd.app/myusername</option>
                    </select>

                    <button
                      className="bg-accent hover:bg-accent-dark text-white px-3 py-1 rounded text-sm flex items-center justify-center transition-colors"
                      onClick={() => addDnsRecord('myproject')}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                      Add Record
                    </button>
                  </div>
                </div>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="text-left text-secondary text-xs border-b border-primary">
                      <th className="px-4 py-2">Type</th>
                      <th className="px-4 py-2">Name</th>
                      <th className="px-4 py-2">Value</th>
                      <th className="px-4 py-2">TTL</th>
                      <th className="px-4 py-2">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {dnsRecords['myproject'].map((record, index) => (
                      <tr key={index} className="border-b border-primary hover:bg-primary-light transition-colors">
                        <td className="px-4 py-3">
                          <span className={`px-2 py-0.5 rounded text-xs ${
                            record.type === 'A' ? 'bg-blue-900 text-blue-200' :
                            record.type === 'CNAME' ? 'bg-green-900 text-green-200' :
                            record.type === 'MX' ? 'bg-purple-900 text-purple-200' :
                            'bg-gray-900 text-gray-200'
                          }`}>
                            {record.type}
                          </span>
                        </td>
                        <td className="px-4 py-3 text-white text-sm">
                          {record.name}
                        </td>
                        <td className="px-4 py-3 text-white text-sm">
                          {record.value}
                        </td>
                        <td className="px-4 py-3 text-secondary text-sm">
                          {record.ttl}
                        </td>
                        <td className="px-4 py-3">
                          <div className="flex space-x-1">
                            <button className="p-1 text-blue-400 hover:text-blue-300" title="Edit">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                              </svg>
                            </button>
                            <button className="p-1 text-red-400 hover:text-red-300" title="Delete">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                              </svg>
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Add DNS Record Form */}
            <div className="bg-secondary rounded shadow p-4">
              <h3 className="text-md font-bold text-white mb-3">Add DNS Record</h3>

              <form className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-secondary text-xs mb-1">Record Type</label>
                  <select className="bg-primary text-white px-3 py-1 rounded text-sm w-full">
                    <option value="A">A (Address)</option>
                    <option value="CNAME">CNAME (Canonical Name)</option>
                    <option value="MX">MX (Mail Exchange)</option>
                    <option value="TXT">TXT (Text)</option>
                  </select>
                </div>

                <div>
                  <label className="block text-secondary text-xs mb-1">Name</label>
                  <input type="text" className="bg-primary text-white px-3 py-1 rounded text-sm w-full" placeholder="e.g. @ or www" />
                </div>

                <div>
                  <label className="block text-secondary text-xs mb-1">Value</label>
                  <input type="text" className="bg-primary text-white px-3 py-1 rounded text-sm w-full" placeholder="e.g. *********" />
                </div>

                <div>
                  <label className="block text-secondary text-xs mb-1">TTL (seconds)</label>
                  <input type="number" className="bg-primary text-white px-3 py-1 rounded text-sm w-full" placeholder="3600" />
                </div>

                <div className="md:col-span-2">
                  <button type="submit" className="bg-accent hover:bg-accent-dark text-white px-3 py-1 rounded text-sm transition-colors">
                    Add Record
                  </button>
                </div>
              </form>
            </div>
          </div>

          {/* API Keys Tab */}
          <div className={`${activeTab === 'api' ? 'block' : 'hidden'}`}>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="bg-secondary rounded-lg shadow-lg p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-secondary text-sm">Total API Keys</p>
                    <h3 className="text-white text-2xl font-bold">{apiKeys.length}</h3>
                  </div>
                  <div className="bg-primary p-3 rounded-full">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-accent" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                    </svg>
                  </div>
                </div>
              </div>

              <div className="bg-secondary rounded-lg shadow-lg p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-secondary text-sm">API Status</p>
                    <h3 className="text-white text-2xl font-bold">Coming Soon</h3>
                  </div>
                  <div className="bg-primary p-3 rounded-full">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-accent" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                </div>
              </div>

              <div className="bg-secondary rounded-lg shadow-lg p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-secondary text-sm">API Requests (30d)</p>
                    <h3 className="text-white text-2xl font-bold">0</h3>
                  </div>
                  <div className="bg-primary p-3 rounded-full">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-accent" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-secondary rounded-lg shadow-lg mb-8">
              <div className="p-6 border-b border-primary">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                  <h2 className="text-xl font-bold text-white mb-4 md:mb-0">Your API Keys</h2>

                  <button
                    className="bg-accent hover:bg-accent-dark text-white px-4 py-2 rounded flex items-center justify-center transition-colors"
                    onClick={createApiKey}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Create API Key
                  </button>
                </div>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="text-left text-secondary text-sm border-b border-primary">
                      <th className="px-6 py-3">Name</th>
                      <th className="px-6 py-3">Key</th>
                      <th className="px-6 py-3">Created</th>
                      <th className="px-6 py-3">Last Used</th>
                      <th className="px-6 py-3">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {apiKeys.map((key) => (
                      <tr key={key.id} className="border-b border-primary hover:bg-primary-light transition-colors">
                        <td className="px-6 py-4 text-white font-medium">
                          {key.name}
                        </td>
                        <td className="px-6 py-4">
                          <div className="flex items-center">
                            <code className="bg-primary px-3 py-1 rounded text-accent font-mono">
                              {key.key.substring(0, 8)}...{key.key.substring(key.key.length - 4)}
                            </code>
                            <button className="ml-2 text-secondary hover:text-white" title="Copy to clipboard">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                              </svg>
                            </button>
                          </div>
                        </td>
                        <td className="px-6 py-4 text-secondary">
                          {key.created}
                        </td>
                        <td className="px-6 py-4 text-secondary">
                          {key.lastUsed}
                        </td>
                        <td className="px-6 py-4">
                          <div className="flex space-x-2">
                            <button className="p-1 text-yellow-400 hover:text-yellow-300" title="Rotate Key">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                              </svg>
                            </button>
                            <button
                              className="p-1 text-red-400 hover:text-red-300"
                              title="Delete"
                              onClick={() => deleteApiKey(key.id)}
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                              </svg>
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {apiKeys.length === 0 && (
                <div className="p-8 text-center">
                  <p className="text-secondary mb-4">You don't have any API keys yet.</p>
                  <button
                    className="bg-accent hover:bg-accent-dark text-white px-4 py-2 rounded transition-colors"
                    onClick={createApiKey}
                  >
                    Create Your First API Key
                  </button>
                </div>
              )}
            </div>

            {/* API Documentation Preview */}
            <div className="bg-secondary rounded-lg shadow-lg p-6">
              <h3 className="text-lg font-bold text-white mb-4">API Documentation</h3>

              <div className="bg-primary p-4 rounded mb-4">
                <p className="text-secondary mb-2">Example API Usage (Coming Soon)</p>
                <pre className="font-mono text-white text-sm overflow-x-auto">
                  <code>
{`// Initialize the SDK
const ZKDomains = require('@zkd-app/sdk');
const api = new ZKDomains({ apiKey: 'YOUR_API_KEY' });

// List all domains
const domains = await api.domains.list();
console.log(domains);

// Register a new domain
const newDomain = await api.domains.create({
  name: 'myproject',
  type: 'project'
});`}
                  </code>
                </pre>
              </div>

              <div className="flex justify-between items-center">
                <p className="text-secondary">
                  Our API is currently in development. Stay tuned for updates!
                </p>

                <button className="bg-primary hover:bg-primary-light text-white px-4 py-2 rounded transition-colors">
                  View Documentation
                </button>
              </div>
            </div>
          </div>

          {/* Settings Tab */}
          <div className={`${activeTab === 'settings' ? 'block' : 'hidden'}`}>
            {/* Account Overview */}
            <div className="bg-secondary rounded-lg shadow-lg p-6 mb-8">
              <div className="flex flex-col md:flex-row items-start">
                <div className="bg-primary p-4 rounded-full mb-4 md:mb-0 md:mr-6">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-accent" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                </div>

                <div className="flex-grow">
                  <h2 className="text-xl font-bold text-white mb-2">user123</h2>
                  <p className="text-secondary mb-4"><EMAIL></p>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <p className="text-secondary">Account Type</p>
                      <p className="text-white font-medium">Pro</p>
                    </div>
                    <div>
                      <p className="text-secondary">Created</p>
                      <p className="text-white font-medium">2023-01-15</p>
                    </div>
                    <div>
                      <p className="text-secondary">Domains</p>
                      <p className="text-white font-medium">2/10</p>
                    </div>
                    <div>
                      <p className="text-secondary">Status</p>
                      <p className="text-green-400 font-medium">Active</p>
                    </div>
                  </div>
                </div>

                <div className="mt-4 md:mt-0">
                  <span className="bg-accent text-white text-xs px-2 py-1 rounded">PRO</span>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Account Settings */}
              <div className="bg-secondary rounded-lg shadow-lg overflow-hidden">
                <div className="p-6 border-b border-primary">
                  <h2 className="text-lg font-bold text-white">Account Settings</h2>
                </div>

                <div className="p-6">
                  <form>
                    <div className="mb-4">
                      <label className="block text-secondary text-sm mb-2">Email Address</label>
                      <input
                        type="email"
                        className="w-full bg-primary text-white px-4 py-2 rounded border border-primary"
                        defaultValue="<EMAIL>"
                      />
                    </div>

                    <div className="mb-4">
                      <label className="block text-secondary text-sm mb-2">Username</label>
                      <input
                        type="text"
                        className="w-full bg-primary text-white px-4 py-2 rounded border border-primary opacity-75 cursor-not-allowed"
                        defaultValue="user123"
                        disabled
                      />
                      <p className="text-secondary text-xs mt-1">Username cannot be changed</p>
                    </div>

                    <div className="mb-4">
                      <label className="block text-secondary text-sm mb-2">Full Name</label>
                      <input
                        type="text"
                        className="w-full bg-primary text-white px-4 py-2 rounded border border-primary"
                        defaultValue="User Example"
                      />
                    </div>

                    <button className="bg-accent hover:bg-accent-dark text-white px-4 py-2 rounded transition-colors">
                      Update Account
                    </button>
                  </form>
                </div>
              </div>

              {/* Security */}
              <div>
                <div className="bg-secondary rounded-lg shadow-lg overflow-hidden mb-8">
                  <div className="p-6 border-b border-primary">
                    <h2 className="text-lg font-bold text-white">Security</h2>
                  </div>

                  <div className="p-6">
                    <form>
                      <div className="mb-4">
                        <label className="block text-secondary text-sm mb-2">Current Password</label>
                        <input
                          type="password"
                          className="w-full bg-primary text-white px-4 py-2 rounded border border-primary"
                          placeholder="••••••••"
                        />
                      </div>

                      <div className="mb-4">
                        <label className="block text-secondary text-sm mb-2">New Password</label>
                        <input
                          type="password"
                          className="w-full bg-primary text-white px-4 py-2 rounded border border-primary"
                          placeholder="••••••••"
                        />
                      </div>

                      <div className="mb-4">
                        <label className="block text-secondary text-sm mb-2">Confirm New Password</label>
                        <input
                          type="password"
                          className="w-full bg-primary text-white px-4 py-2 rounded border border-primary"
                          placeholder="••••••••"
                        />
                      </div>

                      <button className="bg-accent hover:bg-accent-dark text-white px-4 py-2 rounded transition-colors">
                        Change Password
                      </button>
                    </form>
                  </div>
                </div>

                <div className="bg-secondary rounded-lg shadow-lg overflow-hidden">
                  <div className="p-6 border-b border-primary">
                    <h2 className="text-lg font-bold text-white">Two-Factor Authentication</h2>
                  </div>

                  <div className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <p className="text-white font-medium mb-1">Enhance your account security</p>
                        <p className="text-secondary text-sm">Protect your account with an additional layer of security</p>
                      </div>
                      <div className="ml-4">
                        <span className="bg-red-900 text-red-200 text-xs px-2 py-1 rounded">Disabled</span>
                      </div>
                    </div>

                    <button className="bg-primary hover:bg-primary-light text-white px-4 py-2 rounded transition-colors">
                      Enable 2FA
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Danger Zone */}
            <div className="mt-8">
              <div className="bg-secondary rounded-lg shadow-lg overflow-hidden border border-red-800">
                <div className="p-6 border-b border-red-800">
                  <h2 className="text-lg font-bold text-red-400">Danger Zone</h2>
                </div>

                <div className="p-6">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                    <div>
                      <h3 className="text-white font-medium mb-1">Delete Account</h3>
                      <p className="text-secondary text-sm mb-4 md:mb-0">This action is irreversible. All your domains and data will be permanently deleted.</p>
                    </div>

                    <button className="bg-red-800 hover:bg-red-700 text-white px-4 py-2 rounded transition-colors">
                      Delete Account
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>

      {/* Simple Footer */}
      <footer className="bg-secondary py-3 mt-8">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center">
            <div className="text-secondary">
              &copy; {new Date().getFullYear()} zkd.app
            </div>
            <div className="flex space-x-4">
              <Link to="/privacy" className="text-secondary hover:text-white transition-colors">Privacy</Link>
              <Link to="/terms" className="text-secondary hover:text-white transition-colors">Terms</Link>
              <Link to="/contact" className="text-secondary hover:text-white transition-colors">Contact</Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Dashboard;
