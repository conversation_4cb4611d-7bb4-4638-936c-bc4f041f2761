globalThis.__BUILD_MANIFEST = {
  "polyfillFiles": [
    "static/chunks/polyfills.js"
  ],
  "devFiles": [
    "static/chunks/fallback/react-refresh.js"
  ],
  "ampDevFiles": [
    "static/chunks/fallback/webpack.js",
    "static/chunks/fallback/amp.js"
  ],
  "lowPriorityFiles": [],
  "rootMainFiles": [],
  "rootMainFilesTree": {},
  "pages": {
    "/_app": [
      "static/chunks/fallback/webpack.js",
      "static/chunks/fallback/main.js",
      "static/chunks/fallback/pages/_app.js"
    ],
    "/_error": [
      "static/chunks/fallback/webpack.js",
      "static/chunks/fallback/main.js",
      "static/chunks/fallback/pages/_error.js"
    ]
  },
  "ampFirstPages": []
};
globalThis.__BUILD_MANIFEST.lowPriorityFiles = [
"/static/" + process.env.__NEXT_BUILD_ID + "/_buildManifest.js",
,"/static/" + process.env.__NEXT_BUILD_ID + "/_ssgManifest.js",

];