import { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

const Login = () => {
  const navigate = useNavigate();
  const { login } = useAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [isLoggingIn, setIsLoggingIn] = useState(false);
  const [loginError, setLoginError] = useState(null);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoggingIn(true);
    setLoginError(null);

    try {
      const result = await login(email, password);

      if (result.success) {
        navigate('/dashboard');
      } else {
        setLoginError(result.error || 'Invalid credentials');
      }
    } catch (error) {
      console.error('Login error:', error);
      setLoginError(error.message || 'An error occurred during login');
    } finally {
      setIsLoggingIn(false);
    }
  };

  return (
    <div className="bg-primary min-h-screen">
      {/* Simple Header/Navigation */}
      <header className="bg-secondary py-3 shadow-md">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center">
            <Link to="/" className="text-accent text-xl font-bold">ZKD.APP</Link>

            <nav className="flex items-center space-x-6">
              <Link to="/" className="text-white hover:text-accent transition-colors">Home</Link>
              <Link to="/register" className="text-white hover:text-accent transition-colors">Register</Link>
              <Link to="/login" className="text-accent font-medium">Login</Link>
              <Link to="/pricing" className="text-white hover:text-accent transition-colors">Pricing</Link>
              <Link to="/about" className="text-white hover:text-accent transition-colors">About</Link>
              <Link to="/contact" className="text-white hover:text-accent transition-colors">Contact</Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Login Form */}
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-md mx-auto">
          <div className="bg-secondary rounded shadow-lg p-6">
            <h1 className="text-xl font-bold text-white mb-6">Login to Your Account</h1>

            {loginError && (
              <div className="bg-red-900 text-white p-3 rounded mb-4 text-sm">
                {loginError}
              </div>
            )}

            <form onSubmit={handleSubmit}>
              <div className="mb-4">
                <label className="block text-secondary text-sm mb-2">Email Address</label>
                <input
                  type="email"
                  className="w-full bg-primary text-white px-3 py-2 rounded border border-primary"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  required
                />
              </div>

              <div className="mb-6">
                <label className="block text-secondary text-sm mb-2">Password</label>
                <input
                  type="password"
                  className="w-full bg-primary text-white px-3 py-2 rounded border border-primary"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="••••••••"
                  required
                />
                <div className="flex justify-between items-center mt-2">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={rememberMe}
                      onChange={(e) => setRememberMe(e.target.checked)}
                      className="mr-2"
                    />
                    <span className="text-secondary text-xs">Remember me</span>
                  </label>

                  <Link to="/forgot-password" className="text-accent text-xs hover:underline">
                    Forgot Password?
                  </Link>
                </div>
              </div>

              <button
                type="submit"
                className="w-full bg-accent hover:bg-blue-600 text-white py-2 rounded transition-colors"
                disabled={isLoggingIn}
              >
                {isLoggingIn ? (
                  <span className="flex items-center justify-center">
                    <span className="inline-block w-4 h-4 border-2 border-transparent border-t-white rounded-full animate-spin mr-2"></span>
                    Logging in...
                  </span>
                ) : 'Login'}
              </button>

              <div className="mt-4 text-center text-secondary text-sm">
                Don't have an account?{' '}
                <Link to="/register" className="text-accent hover:underline">
                  Register
                </Link>
              </div>
            </form>
          </div>
        </div>
      </div>

      {/* Simple Footer */}
      <footer className="bg-secondary py-3 mt-8">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center">
            <div className="text-secondary">
              &copy; {new Date().getFullYear()} zkd.app
            </div>
            <div className="flex space-x-4">
              <Link to="/privacy" className="text-secondary hover:text-white transition-colors">Privacy</Link>
              <Link to="/terms" className="text-secondary hover:text-white transition-colors">Terms</Link>
              <Link to="/contact" className="text-secondary hover:text-white transition-colors">Contact</Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Login;
