// DNS Management Service
// This service handles all DNS-related operations

import cpanelService from './cpanelService';

// Mock database for DNS records
const dnsRecords = {
  'dom_1': [
    { id: 'rec_1', type: 'A', name: '@', value: '192.168.1.1', ttl: 3600 },
    { id: 'rec_2', type: 'CNAME', name: 'www', value: '@', ttl: 3600 },
    { id: 'rec_3', type: 'MX', name: '@', value: 'mail.example.com', priority: 10, ttl: 3600 }
  ],
  'dom_2': [
    { id: 'rec_4', type: 'A', name: '@', value: '***********', ttl: 3600 },
    { id: 'rec_5', type: 'CNAME', name: 'www', value: '@', ttl: 3600 },
    { id: 'rec_6', type: 'TXT', name: '@', value: 'v=spf1 include:_spf.example.com ~all', ttl: 3600 }
  ]
};

// DNS record templates
const dnsTemplates = {
  'website': [
    { type: 'A', name: '@', value: '', ttl: 3600, description: 'Main domain IP address' },
    { type: 'CNAME', name: 'www', value: '@', ttl: 3600, description: 'Subdomain for www' }
  ],
  'email': [
    { type: 'MX', name: '@', value: '', priority: 10, ttl: 3600, description: 'Mail server' },
    { type: 'TXT', name: '@', value: 'v=spf1 include:_spf.example.com ~all', ttl: 3600, description: 'SPF record' },
    { type: 'TXT', name: '_dmarc', value: 'v=DMARC1; p=none; rua=mailto:<EMAIL>', ttl: 3600, description: 'DMARC record' }
  ],
  'github': [
    { type: 'CNAME', name: '@', value: 'username.github.io', ttl: 3600, description: 'GitHub Pages' },
    { type: 'TXT', name: '@', value: 'github-pages-verification=', ttl: 3600, description: 'GitHub verification' }
  ],
  'vercel': [
    { type: 'A', name: '@', value: '***********', ttl: 3600, description: 'Vercel IP' },
    { type: 'CNAME', name: 'www', value: 'cname.vercel-dns.com', ttl: 3600, description: 'Vercel CNAME' }
  ]
};

const dnsService = {
  /**
   * Get all DNS records for a domain
   * @param {string} domainId - The domain ID
   * @returns {Promise<Array>} - Array of DNS records
   */
  getDomainRecords: async (domainId) => {
    try {
      // In a real app, this would be an API call
      // For now, we'll return mock data
      const records = dnsRecords[domainId] || [];
      return records;
    } catch (error) {
      console.error('Error fetching DNS records:', error);
      throw new Error('Failed to fetch DNS records');
    }
  },

  /**
   * Add a DNS record
   * @param {string} domainId - The domain ID
   * @param {Object} record - The DNS record to add
   * @returns {Promise<Object>} - The created record
   */
  addDnsRecord: async (domainId, record) => {
    try {
      // Validate record
      if (!record.type || !record.name) {
        throw new Error('Record type and name are required');
      }

      // In a real app, this would be an API call
      // For now, we'll update our mock data
      const newRecord = {
        id: 'rec_' + Math.floor(Math.random() * 10000),
        ...record
      };

      // Initialize domain records if they don't exist
      if (!dnsRecords[domainId]) {
        dnsRecords[domainId] = [];
      }

      // Add the new record
      dnsRecords[domainId].push(newRecord);

      return newRecord;
    } catch (error) {
      console.error('Error adding DNS record:', error);
      throw error;
    }
  },

  /**
   * Update a DNS record
   * @param {string} domainId - The domain ID
   * @param {string} recordId - The record ID
   * @param {Object} updates - The updates to apply
   * @returns {Promise<Object>} - The updated record
   */
  updateDnsRecord: async (domainId, recordId, updates) => {
    try {
      // Check if domain exists
      if (!dnsRecords[domainId]) {
        throw new Error('Domain not found');
      }

      // Find the record
      const recordIndex = dnsRecords[domainId].findIndex(r => r.id === recordId);
      if (recordIndex === -1) {
        throw new Error('Record not found');
      }

      // Update the record
      dnsRecords[domainId][recordIndex] = {
        ...dnsRecords[domainId][recordIndex],
        ...updates
      };

      return dnsRecords[domainId][recordIndex];
    } catch (error) {
      console.error('Error updating DNS record:', error);
      throw error;
    }
  },

  /**
   * Delete a DNS record
   * @param {string} domainId - The domain ID
   * @param {string} recordId - The record ID
   * @returns {Promise<boolean>} - Success status
   */
  deleteDnsRecord: async (domainId, recordId) => {
    try {
      // Check if domain exists
      if (!dnsRecords[domainId]) {
        throw new Error('Domain not found');
      }

      // Find the record
      const recordIndex = dnsRecords[domainId].findIndex(r => r.id === recordId);
      if (recordIndex === -1) {
        throw new Error('Record not found');
      }

      // Remove the record
      dnsRecords[domainId].splice(recordIndex, 1);

      return true;
    } catch (error) {
      console.error('Error deleting DNS record:', error);
      throw error;
    }
  },

  /**
   * Get DNS templates
   * @returns {Promise<Object>} - DNS templates
   */
  getDnsTemplates: async () => {
    try {
      return dnsTemplates;
    } catch (error) {
      console.error('Error fetching DNS templates:', error);
      throw new Error('Failed to fetch DNS templates');
    }
  },

  /**
   * Apply a DNS template to a domain
   * @param {string} domainId - The domain ID
   * @param {string} templateName - The template name
   * @returns {Promise<Array>} - The created records
   */
  applyDnsTemplate: async (domainId, templateName) => {
    try {
      // Check if template exists
      if (!dnsTemplates[templateName]) {
        throw new Error('Template not found');
      }

      // Initialize domain records if they don't exist
      if (!dnsRecords[domainId]) {
        dnsRecords[domainId] = [];
      }

      // Add template records
      const createdRecords = [];
      for (const templateRecord of dnsTemplates[templateName]) {
        const newRecord = {
          id: 'rec_' + Math.floor(Math.random() * 10000),
          ...templateRecord
        };

        dnsRecords[domainId].push(newRecord);
        createdRecords.push(newRecord);
      }

      return createdRecords;
    } catch (error) {
      console.error('Error applying DNS template:', error);
      throw error;
    }
  },

  /**
   * Validate a DNS record
   * @param {Object} record - The DNS record to validate
   * @returns {Object} - Validation result
   */
  validateDnsRecord: (record) => {
    const errors = {};

    // Validate type
    if (!record.type) {
      errors.type = 'Record type is required';
    } else if (!['A', 'AAAA', 'CNAME', 'MX', 'TXT', 'NS', 'SRV', 'CAA'].includes(record.type)) {
      errors.type = 'Invalid record type';
    }

    // Validate name
    if (!record.name) {
      errors.name = 'Record name is required';
    }

    // Validate value
    if (!record.value) {
      errors.value = 'Record value is required';
    } else {
      // Type-specific validation
      switch (record.type) {
        case 'A':
          // Simple IPv4 validation
          if (!/^(\d{1,3}\.){3}\d{1,3}$/.test(record.value)) {
            errors.value = 'Invalid IPv4 address';
          }
          break;
        case 'AAAA':
          // Simple IPv6 validation (basic check)
          if (!/^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/.test(record.value)) {
            errors.value = 'Invalid IPv6 address';
          }
          break;
        case 'MX':
          if (record.priority === undefined || record.priority === null) {
            errors.priority = 'Priority is required for MX records';
          } else if (isNaN(record.priority) || record.priority < 0) {
            errors.priority = 'Priority must be a non-negative number';
          }
          break;
      }
    }

    // Validate TTL
    if (!record.ttl) {
      errors.ttl = 'TTL is required';
    } else if (isNaN(record.ttl) || record.ttl < 0) {
      errors.ttl = 'TTL must be a non-negative number';
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }
};

export default dnsService;
