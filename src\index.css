@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background-color: #0f172a;
  color: #e2e8f0;
  line-height: 1.6;
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.btn {
  @apply px-4 py-2 rounded-lg font-medium transition-colors cursor-pointer;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700;
}

.btn-secondary {
  @apply bg-gray-600 text-white hover:bg-gray-700;
}

.btn-danger {
  @apply bg-red-600 text-white hover:bg-red-700;
}

.card {
  @apply bg-slate-800 rounded-lg p-6 shadow-lg;
}

.input {
  @apply w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

.nav-link {
  @apply text-slate-300 hover:text-white transition-colors;
}

.nav-link.active {
  @apply text-blue-400;
}

.loading {
  @apply inline-block w-4 h-4 border-2 border-transparent border-t-blue-500 rounded-full animate-spin;
}
