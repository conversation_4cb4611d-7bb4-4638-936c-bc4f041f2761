import { Navigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

const ProtectedRoute = ({ children }) => {
  const { currentUser, loading } = useAuth();
  
  // If still loading, show nothing
  if (loading) {
    return null;
  }
  
  // If not logged in, redirect to login page
  if (!currentUser) {
    return <Navigate to="/login" />;
  }
  
  // If logged in, show the protected content
  return children;
};

export default ProtectedRoute;
